#!/bin/bash

# Moodle Docker Log Monitor Script
# This script provides comprehensive log monitoring for the Moodle environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Log directories
LOG_DIR="$(dirname "$0")/../logs"
PHP_LOG_DIR="$LOG_DIR/php"
NGINX_LOG_DIR="$LOG_DIR/nginx"
MOODLE_LOG_DIR="$LOG_DIR/moodle"

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_section() {
    echo -e "\n${CYAN}--- $1 ---${NC}"
}

print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

print_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# Function to check if logs directory exists
check_log_directories() {
    print_section "Checking Log Directories"
    
    for dir in "$PHP_LOG_DIR" "$NGINX_LOG_DIR" "$MOODLE_LOG_DIR"; do
        if [ -d "$dir" ]; then
            print_success "Directory exists: $dir"
        else
            print_warning "Creating directory: $dir"
            mkdir -p "$dir"
        fi
    done
}

# Function to show log file sizes
show_log_sizes() {
    print_section "Log File Sizes"
    
    echo -e "${PURPLE}PHP Logs:${NC}"
    if [ -d "$PHP_LOG_DIR" ]; then
        find "$PHP_LOG_DIR" -name "*.log" -exec ls -lh {} \; 2>/dev/null || echo "No PHP log files found"
    fi
    
    echo -e "\n${PURPLE}Nginx Logs:${NC}"
    if [ -d "$NGINX_LOG_DIR" ]; then
        find "$NGINX_LOG_DIR" -name "*.log" -exec ls -lh {} \; 2>/dev/null || echo "No Nginx log files found"
    fi
    
    echo -e "\n${PURPLE}Moodle Logs:${NC}"
    if [ -d "$MOODLE_LOG_DIR" ]; then
        find "$MOODLE_LOG_DIR" -name "*.log" -exec ls -lh {} \; 2>/dev/null || echo "No Moodle log files found"
    fi
}

# Function to tail all logs
tail_all_logs() {
    print_section "Tailing All Logs (Press Ctrl+C to stop)"
    
    # Create array of log files to tail
    LOG_FILES=()
    
    # Add PHP logs
    if [ -f "$PHP_LOG_DIR/error.log" ]; then
        LOG_FILES+=("$PHP_LOG_DIR/error.log")
    fi
    
    # Add Nginx logs
    if [ -f "$NGINX_LOG_DIR/moodle_access.log" ]; then
        LOG_FILES+=("$NGINX_LOG_DIR/moodle_access.log")
    fi
    if [ -f "$NGINX_LOG_DIR/moodle_error.log" ]; then
        LOG_FILES+=("$NGINX_LOG_DIR/moodle_error.log")
    fi
    if [ -f "$NGINX_LOG_DIR/moodle_ssl_access.log" ]; then
        LOG_FILES+=("$NGINX_LOG_DIR/moodle_ssl_access.log")
    fi
    if [ -f "$NGINX_LOG_DIR/moodle_ssl_error.log" ]; then
        LOG_FILES+=("$NGINX_LOG_DIR/moodle_ssl_error.log")
    fi
    
    if [ ${#LOG_FILES[@]} -eq 0 ]; then
        print_warning "No log files found to tail"
        return
    fi
    
    echo "Monitoring files: ${LOG_FILES[*]}"
    tail -f "${LOG_FILES[@]}"
}

# Function to show recent errors
show_recent_errors() {
    print_section "Recent Errors (Last 50 lines)"
    
    echo -e "${PURPLE}PHP Errors:${NC}"
    if [ -f "$PHP_LOG_DIR/error.log" ]; then
        tail -50 "$PHP_LOG_DIR/error.log" | grep -i error || echo "No recent PHP errors"
    else
        echo "No PHP error log found"
    fi
    
    echo -e "\n${PURPLE}Nginx Errors:${NC}"
    if [ -f "$NGINX_LOG_DIR/moodle_error.log" ]; then
        tail -50 "$NGINX_LOG_DIR/moodle_error.log" || echo "No recent Nginx errors"
    else
        echo "No Nginx error log found"
    fi
    
    if [ -f "$NGINX_LOG_DIR/moodle_ssl_error.log" ]; then
        echo -e "\n${PURPLE}Nginx SSL Errors:${NC}"
        tail -50 "$NGINX_LOG_DIR/moodle_ssl_error.log" || echo "No recent Nginx SSL errors"
    fi
}

# Function to show access log summary
show_access_summary() {
    print_section "Access Log Summary (Last 100 requests)"
    
    if [ -f "$NGINX_LOG_DIR/moodle_access.log" ]; then
        echo -e "${PURPLE}HTTP Access Summary:${NC}"
        tail -100 "$NGINX_LOG_DIR/moodle_access.log" | awk '{print $9}' | sort | uniq -c | sort -nr
        
        echo -e "\n${PURPLE}Top Requested URLs:${NC}"
        tail -100 "$NGINX_LOG_DIR/moodle_access.log" | awk '{print $7}' | sort | uniq -c | sort -nr | head -10
    else
        echo "No HTTP access log found"
    fi
    
    if [ -f "$NGINX_LOG_DIR/moodle_ssl_access.log" ]; then
        echo -e "\n${PURPLE}HTTPS Access Summary:${NC}"
        tail -100 "$NGINX_LOG_DIR/moodle_ssl_access.log" | awk '{print $9}' | sort | uniq -c | sort -nr
    fi
}

# Function to check for common issues
check_common_issues() {
    print_section "Checking for Common Issues"
    
    # Check for PHP fatal errors
    if [ -f "$PHP_LOG_DIR/error.log" ]; then
        FATAL_ERRORS=$(grep -c "Fatal error" "$PHP_LOG_DIR/error.log" 2>/dev/null || echo "0")
        if [ "$FATAL_ERRORS" -gt 0 ]; then
            print_error "Found $FATAL_ERRORS PHP fatal errors"
        else
            print_success "No PHP fatal errors found"
        fi
    fi
    
    # Check for 5xx errors in nginx
    if [ -f "$NGINX_LOG_DIR/moodle_access.log" ]; then
        SERVER_ERRORS=$(grep " 5[0-9][0-9] " "$NGINX_LOG_DIR/moodle_access.log" 2>/dev/null | wc -l || echo "0")
        if [ "$SERVER_ERRORS" -gt 0 ]; then
            print_error "Found $SERVER_ERRORS server errors (5xx) in access log"
        else
            print_success "No server errors (5xx) found in access log"
        fi
    fi
    
    # Check for session issues
    if [ -f "$PHP_LOG_DIR/error.log" ]; then
        SESSION_ERRORS=$(grep -c -i "session" "$PHP_LOG_DIR/error.log" 2>/dev/null || echo "0")
        if [ "$SESSION_ERRORS" -gt 0 ]; then
            print_warning "Found $SESSION_ERRORS session-related messages"
        else
            print_success "No session issues found"
        fi
    fi
}

# Function to clear old logs
clear_old_logs() {
    print_section "Clearing Old Logs"
    
    read -p "Are you sure you want to clear all logs? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        find "$LOG_DIR" -name "*.log" -exec truncate -s 0 {} \;
        print_success "All logs cleared"
    else
        echo "Log clearing cancelled"
    fi
}

# Main menu
show_menu() {
    print_header "Moodle Docker Log Monitor"
    echo "1. Check log directories"
    echo "2. Show log file sizes"
    echo "3. Show recent errors"
    echo "4. Show access log summary"
    echo "5. Check for common issues"
    echo "6. Tail all logs (real-time)"
    echo "7. Clear old logs"
    echo "8. Exit"
    echo
}

# Main script logic
main() {
    if [ "$1" = "--tail" ]; then
        check_log_directories
        tail_all_logs
        exit 0
    fi
    
    if [ "$1" = "--errors" ]; then
        check_log_directories
        show_recent_errors
        exit 0
    fi
    
    if [ "$1" = "--check" ]; then
        check_log_directories
        check_common_issues
        exit 0
    fi
    
    while true; do
        show_menu
        read -p "Select an option (1-8): " choice
        
        case $choice in
            1) check_log_directories ;;
            2) show_log_sizes ;;
            3) show_recent_errors ;;
            4) show_access_summary ;;
            5) check_common_issues ;;
            6) tail_all_logs ;;
            7) clear_old_logs ;;
            8) echo "Goodbye!"; exit 0 ;;
            *) print_error "Invalid option. Please select 1-8." ;;
        esac
        
        echo
        read -p "Press Enter to continue..."
        clear
    done
}

# Run main function
main "$@"
