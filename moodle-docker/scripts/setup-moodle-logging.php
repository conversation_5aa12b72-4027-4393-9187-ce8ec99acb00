<?php
/**
 * Moodle Logging Configuration Script
 * This script configures comprehensive logging for Moodle
 */

define('CLI_SCRIPT', true);
require_once(__DIR__ . '/../moodle/config.php');
require_once($CFG->libdir . '/clilib.php');

// Get CLI options
list($options, $unrecognized) = cli_get_params(
    array(
        'help' => false,
        'enable' => false,
        'disable' => false,
        'status' => false,
        'configure' => false
    ),
    array(
        'h' => 'help',
        'e' => 'enable',
        'd' => 'disable',
        's' => 'status',
        'c' => 'configure'
    )
);

if ($options['help']) {
    $help = "
Moodle Logging Configuration Script

Options:
-h, --help          Print out this help
-e, --enable        Enable comprehensive logging
-d, --disable       Disable debug logging
-s, --status        Show current logging status
-c, --configure     Configure logging settings

Examples:
php setup-moodle-logging.php --enable
php setup-moodle-logging.php --status
php setup-moodle-logging.php --configure
";
    echo $help;
    exit(0);
}

/**
 * Enable comprehensive logging
 */
function enable_logging() {
    global $DB;
    
    echo "Enabling comprehensive Moodle logging...\n";
    
    // Enable standard log store
    $logstore = $DB->get_record('config_plugins', array('plugin' => 'logstore_standard', 'name' => 'enabled'));
    if (!$logstore) {
        $DB->insert_record('config_plugins', (object)array(
            'plugin' => 'logstore_standard',
            'name' => 'enabled',
            'value' => '1'
        ));
    } else {
        $DB->update_record('config_plugins', (object)array(
            'id' => $logstore->id,
            'value' => '1'
        ));
    }
    
    // Configure log settings
    $settings = array(
        'logguests' => '1',
        'loglifetime' => '365', // Keep logs for 1 year
        'enablestats' => '1',
        'statsfirstrun' => 'none',
        'statsmaxruntime' => '0',
        'statsruntimedays' => '31',
        'statsruntimestarthour' => '0',
        'statsruntimestartminute' => '0',
        'statsuserthreshold' => '0'
    );
    
    foreach ($settings as $name => $value) {
        $config = $DB->get_record('config', array('name' => $name));
        if (!$config) {
            $DB->insert_record('config', (object)array(
                'name' => $name,
                'value' => $value
            ));
        } else {
            $DB->update_record('config', (object)array(
                'id' => $config->id,
                'value' => $value
            ));
        }
    }
    
    echo "✓ Standard log store enabled\n";
    echo "✓ Guest logging enabled\n";
    echo "✓ Log lifetime set to 365 days\n";
    echo "✓ Statistics enabled\n";
    
    // Clear cache
    purge_all_caches();
    echo "✓ Caches purged\n";
    
    echo "Comprehensive logging enabled successfully!\n";
}

/**
 * Disable debug logging
 */
function disable_debug_logging() {
    global $DB;
    
    echo "Disabling debug logging...\n";
    
    // Set debug to minimal
    $config = $DB->get_record('config', array('name' => 'debug'));
    if ($config) {
        $DB->update_record('config', (object)array(
            'id' => $config->id,
            'value' => '0'
        ));
    }
    
    // Disable debug display
    $config = $DB->get_record('config', array('name' => 'debugdisplay'));
    if ($config) {
        $DB->update_record('config', (object)array(
            'id' => $config->id,
            'value' => '0'
        ));
    }
    
    echo "✓ Debug logging disabled\n";
    
    // Clear cache
    purge_all_caches();
    echo "✓ Caches purged\n";
    
    echo "Debug logging disabled successfully!\n";
}

/**
 * Show current logging status
 */
function show_logging_status() {
    global $DB, $CFG;
    
    echo "=== Moodle Logging Status ===\n\n";
    
    // Debug settings
    echo "Debug Settings:\n";
    echo "- Debug level: " . ($CFG->debug ?? 'Not set') . "\n";
    echo "- Debug display: " . ($CFG->debugdisplay ?? 'Not set') . "\n";
    echo "- Debug SMTP: " . ($CFG->debugsmtp ?? 'Not set') . "\n";
    
    // Log store settings
    echo "\nLog Store Settings:\n";
    $logstore = $DB->get_record('config_plugins', array('plugin' => 'logstore_standard', 'name' => 'enabled'));
    echo "- Standard log store: " . ($logstore && $logstore->value ? 'Enabled' : 'Disabled') . "\n";
    
    // General logging settings
    echo "\nGeneral Logging Settings:\n";
    $logguests = $DB->get_record('config', array('name' => 'logguests'));
    echo "- Log guests: " . ($logguests && $logguests->value ? 'Yes' : 'No') . "\n";
    
    $loglifetime = $DB->get_record('config', array('name' => 'loglifetime'));
    echo "- Log lifetime: " . ($loglifetime ? $loglifetime->value . ' days' : 'Not set') . "\n";
    
    $enablestats = $DB->get_record('config', array('name' => 'enablestats'));
    echo "- Statistics: " . ($enablestats && $enablestats->value ? 'Enabled' : 'Disabled') . "\n";
    
    // Log file locations
    echo "\nLog File Locations:\n";
    echo "- PHP error log: /var/log/php/error.log\n";
    echo "- Nginx access log: /var/log/nginx/moodle_access.log\n";
    echo "- Nginx error log: /var/log/nginx/moodle_error.log\n";
    echo "- Moodle data directory: " . $CFG->dataroot . "\n";
    
    // Recent log entries count
    echo "\nRecent Activity:\n";
    try {
        $recent_logs = $DB->count_records_sql("SELECT COUNT(*) FROM {logstore_standard_log} WHERE timecreated > ?", array(time() - 3600));
        echo "- Log entries in last hour: " . $recent_logs . "\n";
        
        $total_logs = $DB->count_records('logstore_standard_log');
        echo "- Total log entries: " . $total_logs . "\n";
    } catch (Exception $e) {
        echo "- Could not retrieve log statistics: " . $e->getMessage() . "\n";
    }
}

/**
 * Configure advanced logging settings
 */
function configure_logging() {
    global $DB;
    
    echo "Configuring advanced logging settings...\n";
    
    // Advanced logging configurations
    $advanced_settings = array(
        'logstore_standard_log_guest' => '1',
        'logstore_standard_log_lifetime' => '365',
        'logstore_standard_buffersize' => '50',
        'enablewebserviceslogging' => '1',
        'debugpdb' => '0',
        'debugsqltrace' => '0'
    );
    
    foreach ($advanced_settings as $name => $value) {
        if (strpos($name, 'logstore_') === 0) {
            // Plugin setting
            $plugin = 'logstore_standard';
            $setting_name = str_replace('logstore_standard_', '', $name);
            
            $config = $DB->get_record('config_plugins', array('plugin' => $plugin, 'name' => $setting_name));
            if (!$config) {
                $DB->insert_record('config_plugins', (object)array(
                    'plugin' => $plugin,
                    'name' => $setting_name,
                    'value' => $value
                ));
            } else {
                $DB->update_record('config_plugins', (object)array(
                    'id' => $config->id,
                    'value' => $value
                ));
            }
        } else {
            // Global setting
            $config = $DB->get_record('config', array('name' => $name));
            if (!$config) {
                $DB->insert_record('config', (object)array(
                    'name' => $name,
                    'value' => $value
                ));
            } else {
                $DB->update_record('config', (object)array(
                    'id' => $config->id,
                    'value' => $value
                ));
            }
        }
    }
    
    echo "✓ Advanced logging settings configured\n";
    
    // Clear cache
    purge_all_caches();
    echo "✓ Caches purged\n";
    
    echo "Advanced logging configuration completed!\n";
}

// Main execution
if ($options['enable']) {
    enable_logging();
} elseif ($options['disable']) {
    disable_debug_logging();
} elseif ($options['status']) {
    show_logging_status();
} elseif ($options['configure']) {
    configure_logging();
} else {
    echo "Use --help to see available options\n";
    show_logging_status();
}

echo "\nDone.\n";
?>
