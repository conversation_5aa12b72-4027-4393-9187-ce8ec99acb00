#!/bin/bash

# Moodle 5.0 PHP Extensions Verification Script
# This script verifies all required and recommended PHP extensions are installed

echo "=== Moodle 5.0 PHP Extensions Verification ==="
echo

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ Error: Containers are not running!"
    echo "Please start containers first: docker-compose up -d"
    exit 1
fi

echo "🔍 Checking PHP version..."
PHP_VERSION=$(docker-compose exec -T php php -v | head -n 1)
echo "   $PHP_VERSION"

if [[ $PHP_VERSION == *"8.3"* ]]; then
    echo "   ✅ PHP 8.3 detected (Moodle 5.0 compatible)"
elif [[ $PHP_VERSION == *"8.2"* ]] || [[ $PHP_VERSION == *"8.4"* ]]; then
    echo "   ✅ PHP version compatible with Moodle 5.0"
else
    echo "   ❌ PHP version may not be compatible with Moodle 5.0"
fi

echo
echo "🔍 Checking REQUIRED PHP extensions..."

# Required extensions according to Moodle 5.0 docs
REQUIRED_EXTENSIONS=(
    "ctype"
    "curl"
    "dom"
    "gd"
    "iconv"
    "intl"
    "json"
    "mbstring"
    "mysqli"
    "pcre"
    "simplexml"
    "spl"
    "xml"
    "zip"
)

MISSING_REQUIRED=()

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    # Check if extension is loaded (some are built-in and don't show in php -m)
    if docker-compose exec -T php php -r "echo extension_loaded('$ext') ? 'loaded' : 'missing';" | grep -q "loaded"; then
        echo "   ✅ $ext"
    else
        echo "   ❌ $ext (MISSING - REQUIRED)"
        MISSING_REQUIRED+=("$ext")
    fi
done

echo
echo "🔍 Checking RECOMMENDED PHP extensions..."

# Recommended extensions according to Moodle 5.0 docs
RECOMMENDED_EXTENSIONS=(
    "openssl"
    "soap"
    "sodium"
    "tokenizer"
    "xmlrpc"
)

MISSING_RECOMMENDED=()

for ext in "${RECOMMENDED_EXTENSIONS[@]}"; do
    # Special handling for xmlrpc (removed in PHP 8.0+)
    if [ "$ext" = "xmlrpc" ]; then
        echo "   ⚠️  $ext (Not available in PHP 8.0+ - use alternative)"
        continue
    fi

    if docker-compose exec -T php php -r "echo extension_loaded('$ext') ? 'loaded' : 'missing';" | grep -q "loaded"; then
        echo "   ✅ $ext"
    else
        echo "   ⚠️  $ext (MISSING - RECOMMENDED)"
        MISSING_RECOMMENDED+=("$ext")
    fi
done

echo
echo "🔍 Checking ADDITIONAL useful extensions..."

# Additional useful extensions
ADDITIONAL_EXTENSIONS=(
    "exif"
    "opcache"
    "pdo"
    "pdo_mysql"
    "ldap"
    "pgsql"
    "pdo_pgsql"
)

for ext in "${ADDITIONAL_EXTENSIONS[@]}"; do
    if docker-compose exec -T php php -m | grep -q "^$ext$"; then
        echo "   ✅ $ext"
    else
        echo "   ⚠️  $ext (not installed)"
    fi
done

echo
echo "=== SUMMARY ==="

if [ ${#MISSING_REQUIRED[@]} -eq 0 ]; then
    echo "✅ All REQUIRED extensions are installed!"
else
    echo "❌ Missing REQUIRED extensions: ${MISSING_REQUIRED[*]}"
    echo "   These MUST be installed for Moodle to work properly."
fi

if [ ${#MISSING_RECOMMENDED[@]} -eq 0 ]; then
    echo "✅ All RECOMMENDED extensions are installed!"
else
    echo "⚠️  Missing RECOMMENDED extensions: ${MISSING_RECOMMENDED[*]}"
    echo "   These should be installed for optimal functionality."
fi

echo
echo "🔍 Checking PHP configuration..."
echo "   max_input_vars: $(docker-compose exec -T php php -r "echo ini_get('max_input_vars');")"
echo "   memory_limit: $(docker-compose exec -T php php -r "echo ini_get('memory_limit');")"
echo "   file_uploads: $(docker-compose exec -T php php -r "echo ini_get('file_uploads') ? 'On' : 'Off';")"

echo
if [ ${#MISSING_REQUIRED[@]} -eq 0 ]; then
    echo "🎉 PHP setup is ready for Moodle 5.0!"
else
    echo "🔧 Please rebuild containers to install missing extensions:"
    echo "   docker-compose down"
    echo "   docker-compose up --build"
fi
