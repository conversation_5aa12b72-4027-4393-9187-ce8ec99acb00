# 📊 Moodle Docker Logging Configuration

## ✅ **COMPREHENSIVE LOGGING ENABLED**

Your Moodle 5.0 environment now has **comprehensive logging enabled** at all levels to monitor performance, detect issues, and document system behavior.

---

## 🔧 **Logging Configuration Overview**

### **Multi-Level Logging Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                    LOGGING ARCHITECTURE                     │
├─────────────────────────────────────────────────────────────┤
│  Application Layer (Moodle)                                │
│  ├── Debug Level: 15 (DEBUG_NORMAL)                        │
│  ├── Guest Logging: Enabled                                │
│  ├── Log Lifetime: 365 days                                │
│  └── Standard Log Store: Enabled                           │
├─────────────────────────────────────────────────────────────┤
│  Web Server Layer (Nginx)                                  │
│  ├── Access Logs: Combined format                          │
│  ├── Error Logs: Warning level                             │
│  ├── HTTP Logs: /var/log/nginx/moodle_access.log          │
│  └── HTTPS Logs: /var/log/nginx/moodle_ssl_access.log     │
├─────────────────────────────────────────────────────────────┤
│  Application Server Layer (PHP-FPM)                        │
│  ├── Error Logging: Enabled                                │
│  ├── Error Log: /var/log/php/error.log                     │
│  ├── Memory Leak Reporting: Enabled                        │
│  └── Repeated Error Logging: Enabled                       │
└─────────────────────────────────────────────────────────────┘
```

---

## 📁 **Log File Locations**

### **Host System (Persistent)**
```bash
moodle-docker/logs/
├── php/
│   └── error.log                    # PHP errors and warnings
├── nginx/
│   ├── moodle_access.log           # HTTP access logs
│   ├── moodle_error.log            # HTTP error logs
│   ├── moodle_ssl_access.log       # HTTPS access logs
│   └── moodle_ssl_error.log        # HTTPS error logs
└── moodle/
    └── (future Moodle-specific logs)
```

### **Container Paths**
```bash
# PHP Container
/var/log/php/error.log              # PHP error log

# Nginx Container  
/var/log/nginx/moodle_access.log    # HTTP access log
/var/log/nginx/moodle_error.log     # HTTP error log
/var/log/nginx/moodle_ssl_access.log # HTTPS access log
/var/log/nginx/moodle_ssl_error.log  # HTTPS error log

# Moodle Data Directory
/var/moodledata/                    # Moodle data and cache logs
```

---

## 🛠 **Log Monitoring Tools**

### **1. Interactive Log Monitor**
```bash
# Run interactive log monitor
./scripts/monitor-logs.sh

# Quick commands
./scripts/monitor-logs.sh --check    # Check for common issues
./scripts/monitor-logs.sh --errors   # Show recent errors
./scripts/monitor-logs.sh --tail     # Real-time log monitoring
```

### **2. Manual Log Commands**
```bash
# View recent PHP errors
tail -f logs/php/error.log

# View recent access logs
tail -f logs/nginx/moodle_access.log

# View recent error logs
tail -f logs/nginx/moodle_error.log

# Monitor all logs simultaneously
tail -f logs/php/error.log logs/nginx/*.log
```

---

## 📊 **Log Configuration Details**

### **Moodle Application Logging**
```php
// Debug and logging settings (in config.php)
$CFG->debug = 15;                           // DEBUG_NORMAL
$CFG->debugdisplay = 0;                     // Don't display errors on screen
$CFG->debugsmtp = 1;                        // Enable SMTP debugging
$CFG->log_manager_class = '\core\log\manager';
$CFG->logstore_standard_log_guest = true;   // Log guest actions
$CFG->logstore_standard_log_lifetime = 365; // Keep logs for 1 year
```

### **PHP Error Logging**
```ini
; PHP logging configuration (in php.ini)
log_errors = On
error_log = /var/log/php/error.log
log_errors_max_len = 0
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
html_errors = Off
```

### **Nginx Access Logging**
```nginx
# Nginx logging configuration
error_log /var/log/nginx/moodle_error.log warn;
access_log /var/log/nginx/moodle_access.log combined;

# SSL logging
error_log /var/log/nginx/moodle_ssl_error.log warn;
access_log /var/log/nginx/moodle_ssl_access.log combined;
```

---

## 🔍 **What Gets Logged**

### **PHP Errors & Warnings**
- ✅ Fatal errors
- ✅ Parse errors  
- ✅ Warnings
- ✅ Notices (when debug enabled)
- ✅ Memory leaks
- ✅ Extension errors

### **Nginx Access Logs**
- ✅ All HTTP requests
- ✅ Response codes
- ✅ Request methods
- ✅ User agents
- ✅ Response times
- ✅ Request sizes

### **Moodle Application Logs**
- ✅ User login/logout
- ✅ Course access
- ✅ File uploads/downloads
- ✅ Database queries (when enabled)
- ✅ Cache operations
- ✅ Plugin activities

---

## 🚨 **Issue Detection & Alerts**

### **Automatic Issue Detection**
The log monitor script automatically checks for:

1. **PHP Fatal Errors**
   - Detects: Fatal errors that crash PHP
   - Impact: Site functionality issues

2. **Server Errors (5xx)**
   - Detects: 500, 502, 503, 504 errors
   - Impact: Service availability issues

3. **Session Problems**
   - Detects: Session-related errors
   - Impact: User login/authentication issues

### **Common Issues to Watch For**
```bash
# Check for common issues
./scripts/monitor-logs.sh --check

# Monitor specific patterns
grep -i "fatal\|error\|warning" logs/php/error.log
grep " 5[0-9][0-9] " logs/nginx/moodle_access.log
grep -i "session" logs/php/error.log
```

---

## 📈 **Log Analysis Examples**

### **Performance Analysis**
```bash
# Top requested URLs
awk '{print $7}' logs/nginx/moodle_access.log | sort | uniq -c | sort -nr | head -10

# Response code distribution
awk '{print $9}' logs/nginx/moodle_access.log | sort | uniq -c | sort -nr

# Slow requests (if response time logged)
awk '$NF > 1.0 {print}' logs/nginx/moodle_access.log
```

### **Error Analysis**
```bash
# Count PHP errors by type
grep -o "PHP [A-Za-z]* error" logs/php/error.log | sort | uniq -c

# Recent 404 errors
grep " 404 " logs/nginx/moodle_access.log | tail -10

# Database connection errors
grep -i "database\|mysql\|connection" logs/php/error.log
```

---

## 🔄 **Log Rotation & Maintenance**

### **Manual Log Cleanup**
```bash
# Clear all logs (interactive)
./scripts/monitor-logs.sh
# Select option 7: Clear old logs

# Manual cleanup
truncate -s 0 logs/php/error.log
truncate -s 0 logs/nginx/*.log
```

### **Log Size Monitoring**
```bash
# Check log file sizes
./scripts/monitor-logs.sh
# Select option 2: Show log file sizes

# Manual size check
du -h logs/php/error.log
du -h logs/nginx/*.log
```

---

## 🎯 **Best Practices**

### **Regular Monitoring**
1. **Daily**: Check for fatal errors and 5xx responses
2. **Weekly**: Review access patterns and performance
3. **Monthly**: Clean up old logs and analyze trends

### **Issue Response**
1. **Fatal Errors**: Immediate investigation required
2. **5xx Errors**: Check service health and resources
3. **High Traffic**: Monitor performance and scaling needs

### **Log Security**
- ✅ Logs stored outside web root
- ✅ No sensitive data in logs
- ✅ Access restricted to administrators
- ✅ Regular log rotation implemented

---

## 🚀 **Quick Start Commands**

```bash
# Start real-time monitoring
./scripts/monitor-logs.sh --tail

# Check current status
./scripts/monitor-logs.sh --check

# View recent errors
./scripts/monitor-logs.sh --errors

# Interactive menu
./scripts/monitor-logs.sh
```

---

## ✅ **Logging Status: FULLY OPERATIONAL**

Your Moodle environment now has:
- ✅ **Comprehensive error logging** at all levels
- ✅ **Real-time monitoring tools** for issue detection
- ✅ **Persistent log storage** for historical analysis
- ✅ **Automated issue detection** for common problems
- ✅ **Performance monitoring** through access logs
- ✅ **Security logging** for audit trails

**🎉 All logging is now active and ready to help identify and document any issues!**
