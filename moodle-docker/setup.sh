#!/bin/bash

# Moodle 5.0 Docker Setup and Verification Script
# This script sets up and verifies the complete Moodle 5.0 environment

set -e

echo "=== Moodle 5.0 Docker Setup Script ==="
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON><PERSON> and <PERSON>er Compose are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Prerequisites check passed!"
}

# Verify .env file exists and has required variables
check_env_file() {
    print_status "Checking .env file..."
    
    if [ ! -f .env ]; then
        print_error ".env file not found. Please create it from .env.example"
        exit 1
    fi
    
    # Check for required variables
    required_vars=("MYSQL_ROOT_PASSWORD" "MYSQL_DATABASE" "MYSQL_USER" "MYSQL_PASSWORD" 
                   "MOODLE_DB_HOST" "MOODLE_DB_NAME" "MOODLE_DB_USER" "MOODLE_DB_PASS"
                   "MOODLE_WWWROOT" "MOODLE_REDIS_HOST" "MOODLE_REDIS_PORT")
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env; then
            print_error "Required variable $var not found in .env file"
            exit 1
        fi
    done
    
    print_status ".env file check passed!"
}

# Build and start services
build_and_start() {
    print_status "Building and starting services..."
    
    # Stop any existing containers
    docker-compose down -v 2>/dev/null || true
    
    # Build the PHP container with new extensions
    print_status "Building PHP container with all required extensions..."
    docker-compose build --no-cache php
    
    # Start all services
    print_status "Starting all services..."
    docker-compose up -d
    
    print_status "Services started!"
}

# Wait for services to be healthy
wait_for_services() {
    print_status "Waiting for services to be healthy..."
    
    # Wait for MySQL
    print_status "Waiting for MySQL to be ready..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            print_status "MySQL is ready!"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "MySQL failed to start within 60 seconds"
        exit 1
    fi
    
    # Wait for Redis
    print_status "Waiting for Redis to be ready..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
            print_status "Redis is ready!"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Redis failed to start within 30 seconds"
        exit 1
    fi
    
    # Wait for PHP-FPM
    print_status "Waiting for PHP-FPM to be ready..."
    sleep 10  # Give PHP-FPM time to start
    
    print_status "All services are ready!"
}

# Run comprehensive checks
run_checks() {
    print_status "Running comprehensive system checks..."
    
    # Check PHP extensions
    print_status "Checking PHP extensions..."
    if docker-compose exec -T php php /usr/local/bin/check-extensions.php; then
        print_status "PHP extensions check passed!"
    else
        print_error "PHP extensions check failed!"
        exit 1
    fi
    
    # Check file permissions
    print_status "Checking file permissions..."
    docker-compose exec -T php ls -la /var/moodledata/ | head -5
    docker-compose exec -T php ls -la /var/www/html/ | head -5
    
    # Check service connectivity
    print_status "Testing service connectivity..."
    
    # Test MySQL connection from PHP container
    if docker-compose exec -T php php -r "
        try {
            \$pdo = new PDO('mysql:host=mysql;dbname='.getenv('MOODLE_DB_NAME'), getenv('MOODLE_DB_USER'), getenv('MOODLE_DB_PASS'));
            echo 'MySQL connection: SUCCESS\n';
        } catch (Exception \$e) {
            echo 'MySQL connection: FAILED - ' . \$e->getMessage() . '\n';
            exit(1);
        }
    "; then
        print_status "MySQL connectivity test passed!"
    else
        print_error "MySQL connectivity test failed!"
        exit 1
    fi
    
    # Test Redis connection from PHP container
    if docker-compose exec -T php php -r "
        try {
            \$redis = new Redis();
            if (\$redis->connect('redis', 6379)) {
                echo 'Redis connection: SUCCESS\n';
                \$redis->close();
            } else {
                echo 'Redis connection: FAILED\n';
                exit(1);
            }
        } catch (Exception \$e) {
            echo 'Redis connection: FAILED - ' . \$e->getMessage() . '\n';
            exit(1);
        }
    "; then
        print_status "Redis connectivity test passed!"
    else
        print_error "Redis connectivity test failed!"
        exit 1
    fi
}

# Display final status and URLs
show_final_status() {
    print_status "=== Setup Complete! ==="
    echo ""
    print_status "Your Moodle 5.0 environment is ready!"
    echo ""
    print_status "Access URLs:"
    echo "  - HTTP:  http://localhost"
    echo "  - HTTPS: https://localhost"
    echo ""
    print_status "Default admin credentials (if not changed):"
    echo "  - Username: admin"
    echo "  - Password: (set during Moodle installation)"
    echo ""
    print_status "Useful commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Check status: docker-compose ps"
    echo "  - Stop services: docker-compose down"
    echo "  - Check PHP extensions: docker-compose exec php php /usr/local/bin/check-extensions.php"
    echo ""
    print_warning "Note: If this is a fresh installation, you'll need to complete the Moodle setup wizard in your browser."
}

# Main execution
main() {
    check_prerequisites
    check_env_file
    build_and_start
    wait_for_services
    run_checks
    show_final_status
}

# Run main function
main "$@"
