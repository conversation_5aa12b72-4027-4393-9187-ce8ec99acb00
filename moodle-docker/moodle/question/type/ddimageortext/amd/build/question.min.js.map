{"version": 3, "file": "question.min.js", "sources": ["../src/question.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/*\n * JavaScript to allow dragging options to slots (using mouse down or touch) or tab through slots using keyboard.\n *\n * @module     qtype_ddimageortext/question\n * @copyright  2018 The Open University\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine([\n    'jquery',\n    'core/dragdrop',\n    'core/key_codes',\n    'core_form/changechecker',\n    'core_filters/events',\n], function(\n    $,\n    dragDrop,\n    keys,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>,\n    filterEvent\n) {\n\n    \"use strict\";\n\n    /**\n     * Initialise one drag-drop onto image question.\n     *\n     * @param {String} containerId id of the outer div for this question.\n     * @param {boolean} readOnly whether the question is being displayed read-only.\n     * @param {Array} places Information about the drop places.\n     * @constructor\n     */\n    function DragDropOntoImageQuestion(containerId, readOnly, places) {\n        this.containerId = containerId;\n        this.questionAnswer = {};\n        this.questionDragDropWidthHeight = [];\n        M.util.js_pending('qtype_ddimageortext-init-' + this.containerId);\n        this.places = places;\n        this.allImagesLoaded = false;\n        this.imageLoadingTimeoutId = null;\n        this.isPrinting = false;\n        if (readOnly) {\n            this.getRoot().addClass('qtype_ddimageortext-readonly');\n        }\n\n        var thisQ = this;\n        this.getNotYetLoadedImages().one('load', function() {\n            thisQ.waitForAllImagesToBeLoaded();\n        });\n        this.waitForAllImagesToBeLoaded();\n    }\n\n    /**\n     * Change all the drags and drops related to the item that has been changed by filter to correct size and content.\n     *\n     *  @param {object} filteredElement the element has been modified by filter.\n     */\n    DragDropOntoImageQuestion.prototype.changeAllDragsAndDropsToFilteredContent = function(filteredElement) {\n        let currentFilteredItem = $(filteredElement);\n        const parentIsDD = currentFilteredItem.parent().closest('div').hasClass('placed') ||\n            currentFilteredItem.parent().hasClass('draghome');\n        const isDD = currentFilteredItem.hasClass('placed') || currentFilteredItem.hasClass('draghome');\n        // The filtered element or parent element should a drag or drop item.\n        if (!parentIsDD && !isDD) {\n            return;\n        }\n        if (parentIsDD) {\n            currentFilteredItem = currentFilteredItem.parent().closest('div');\n        }\n        if (this.getRoot().find(currentFilteredItem).length <= 0) {\n            // If the DD item doesn't belong to this question\n            // In case we have multiple questions in the same page.\n            return;\n        }\n        const group = this.getGroup(currentFilteredItem),\n            choice = this.getChoice(currentFilteredItem);\n        let listOfModifiedDragDrop = [];\n        // Get the list of drag and drop item within the same group and choice.\n        this.getRoot().find('.group' + group + '.choice' + choice).each(function(i, node) {\n            // Same modified item, skip it.\n            if ($(node).get(0) === currentFilteredItem.get(0)) {\n                return;\n            }\n            const originalClass = $(node).attr('class');\n            const originalStyle = $(node).attr('style');\n            // We want to keep all the handler and event for filtered item, so using clone is the only choice.\n            const filteredDragDropClone = currentFilteredItem.clone();\n            // Sometimes, for the question that has a lot of input groups and unlimited draggable items,\n            // this 'clone' process takes longer than usual,it will not add the eventHandler for this cloned drag.\n            // We need to make sure to add the eventHandler for the cloned drag too.\n            questionManager.addEventHandlersToDrag(filteredDragDropClone);\n            // Replace the class and style of the drag drop item we want to replace for the clone.\n            filteredDragDropClone.attr('class', originalClass);\n            filteredDragDropClone.attr('style', originalStyle);\n            // Insert into DOM.\n            $(node).before(filteredDragDropClone);\n            // Add the item has been replaced to a list so we can remove it later.\n            listOfModifiedDragDrop.push(node);\n        });\n\n        listOfModifiedDragDrop.forEach(function(node) {\n            $(node).remove();\n        });\n        // Save the current height and width.\n        const currentHeight = currentFilteredItem.height();\n        const currentWidth = currentFilteredItem.width();\n        // Set to auto, so we can get the real height and width of the filtered item.\n        currentFilteredItem.height('auto');\n        currentFilteredItem.width('auto');\n        // We need to set display block so we can get height and width.\n        // Some browsers can't get the offsetWidth/Height if they are an inline element like span tag.\n        if (!filteredElement.offsetWidth || !filteredElement.offsetHeight) {\n            filteredElement.classList.add('d-block');\n        }\n        if (this.questionDragDropWidthHeight[group].maxWidth < Math.ceil(filteredElement.offsetWidth) ||\n            this.questionDragDropWidthHeight[group].maxHeight < Math.ceil(0 + filteredElement.offsetHeight)) {\n            // Remove the d-block class before calculation.\n            filteredElement.classList.remove('d-block');\n            // Now resize all the items in the same group if we have new maximum width or height.\n            this.resizeAllDragsAndDropsInGroup(group);\n        } else {\n            // Calculate the top padding.\n            const top = Math.floor((this.questionDragDropWidthHeight[group].maxHeight - filteredElement.offsetHeight) / 2);\n            // Set top padding so the content of filtered item is center again.\n            currentFilteredItem.width(currentWidth).height(currentHeight).css({\n                'padding-top': top + 'px',\n            });\n        }\n        // Remove the d-block class after resize.\n        filteredElement.classList.remove('d-block');\n    };\n\n    /**\n     * Waits until all images are loaded before calling setupQuestion().\n     *\n     * This function is called from the onLoad of each image, and also polls with\n     * a time-out, because image on-loads are allegedly unreliable.\n     */\n    DragDropOntoImageQuestion.prototype.waitForAllImagesToBeLoaded = function() {\n        var thisQ = this;\n\n        // This method may get called multiple times (via image on-loads or timeouts.\n        // If we are already done, don't do it again.\n        if (this.allImagesLoaded) {\n            return;\n        }\n\n        // Clear any current timeout, if set.\n        if (this.imageLoadingTimeoutId !== null) {\n            clearTimeout(this.imageLoadingTimeoutId);\n        }\n\n        // If we have not yet loaded all images, set a timeout to\n        // call ourselves again, since apparently images on-load\n        // events are flakey.\n        if (this.getNotYetLoadedImages().length > 0) {\n            this.imageLoadingTimeoutId = setTimeout(function() {\n                thisQ.waitForAllImagesToBeLoaded();\n            }, 100);\n            return;\n        }\n\n        // We now have all images. Carry on, but only after giving the layout a chance to settle down.\n        this.allImagesLoaded = true;\n        thisQ.setupQuestion();\n        // Wait for all dynamic content loaded by filter to be completed.\n        document.addEventListener(filterEvent.eventTypes.filterContentRenderingComplete, (elements) => {\n            elements.detail.nodes.forEach((element) => {\n                thisQ.changeAllDragsAndDropsToFilteredContent(element);\n            });\n        });\n    };\n\n    /**\n     * Get any of the images in the drag-drop area that are not yet fully loaded.\n     *\n     * @returns {jQuery} those images.\n     */\n    DragDropOntoImageQuestion.prototype.getNotYetLoadedImages = function() {\n        var thisQ = this;\n        return this.getRoot().find('.ddarea img').not(function(i, imgNode) {\n            return thisQ.imageIsLoaded(imgNode);\n        });\n    };\n\n    /**\n     * Check if an image has loaded without errors.\n     *\n     * @param {HTMLImageElement} imgElement an image.\n     * @returns {boolean} true if this image has loaded without errors.\n     */\n    DragDropOntoImageQuestion.prototype.imageIsLoaded = function(imgElement) {\n        return imgElement.complete && imgElement.naturalHeight !== 0;\n    };\n\n    /**\n     * Set up the question, once all images have been loaded.\n     */\n    DragDropOntoImageQuestion.prototype.setupQuestion = function() {\n        this.resizeAllDragsAndDrops();\n        this.cloneDrags();\n        this.positionDragsAndDrops();\n        M.util.js_complete('qtype_ddimageortext-init-' + this.containerId);\n    };\n\n    /**\n     * In each group, resize all the items to be the same size.\n     */\n    DragDropOntoImageQuestion.prototype.resizeAllDragsAndDrops = function() {\n        var thisQ = this;\n        this.getRoot().find('.draghomes > div').each(function(i, node) {\n            thisQ.resizeAllDragsAndDropsInGroup(\n                thisQ.getClassnameNumericSuffix($(node), 'dragitemgroup'));\n        });\n    };\n\n    /**\n     * In a given group, set all the drags and drops to be the same size.\n     *\n     * @param {int} group the group number.\n     */\n    DragDropOntoImageQuestion.prototype.resizeAllDragsAndDropsInGroup = function(group) {\n        var root = this.getRoot(),\n            dragHomes = root.find(\".draghome.group\" + group),\n            maxWidth = 0,\n            maxHeight = 0;\n\n        // Find the maximum size of any drag in this groups.\n        dragHomes.each(function(i, drag) {\n            maxWidth = Math.max(maxWidth, Math.ceil(drag.offsetWidth));\n            maxHeight = Math.max(maxHeight, Math.ceil(drag.offsetHeight));\n        });\n\n        // The size we will want to set is a bit bigger than this.\n        maxWidth += 10;\n        maxHeight += 10;\n        this.questionDragDropWidthHeight[group] = {maxWidth, maxHeight};\n\n        // Set each drag home to that size.\n        dragHomes.each(function(i, drag) {\n            const top = Math.floor((maxHeight - drag.offsetHeight) / 2);\n            // Set top padding so the item is centred.\n            $(drag).width(maxWidth).height(maxHeight).css({\n                'padding-top': top + 'px',\n            });\n        });\n\n        // Create the drops and make them the right size.\n        for (var i in this.places) {\n            if (!this.places.hasOwnProperty((i))) {\n                continue;\n            }\n            var place = this.places[i],\n                label = place.text;\n            if (parseInt(place.group) !== group) {\n                continue;\n            }\n            if (label === '') {\n                label = M.util.get_string('blank', 'qtype_ddimageortext');\n            }\n            if (root.find('.dropzones .dropzone.group' + place.group + '.place' + i).length === 0) {\n                root.find('.dropzones').append('<div class=\"dropzone active group' + place.group +\n                    ' place' + i + '\" tabindex=\"0\">' +\n                    '<span class=\"accesshide\">' + label + '</span>&nbsp;</div>');\n            }\n            root.find('.dropzone.place' + i).width(maxWidth - 2).height(maxHeight - 2);\n        }\n    };\n\n    /**\n     * Invisible 'drag homes' are output by the renderer. These have the same properties\n     * as the drag items but are invisible. We clone these invisible elements to make the\n     * actual drag items.\n     */\n    DragDropOntoImageQuestion.prototype.cloneDrags = function() {\n        var thisQ = this;\n        thisQ.getRoot().find('.draghome').each(function(index, dragHome) {\n            var drag = $(dragHome);\n            var placeHolder = drag.clone();\n            placeHolder.removeClass();\n            placeHolder.addClass('draghome choice' +\n                thisQ.getChoice(drag) + ' group' +\n                thisQ.getGroup(drag) + ' dragplaceholder');\n            drag.before(placeHolder);\n        });\n    };\n\n    /**\n     * Clone drag item for one choice.\n     *\n     * @param {jQuery} dragHome the drag home to clone.\n     */\n    DragDropOntoImageQuestion.prototype.cloneDragsForOneChoice = function(dragHome) {\n        if (dragHome.hasClass('infinite')) {\n            var noOfDrags = this.noOfDropsInGroup(this.getGroup(dragHome));\n            for (var i = 0; i < noOfDrags; i++) {\n                this.cloneDrag(dragHome);\n            }\n        } else {\n            this.cloneDrag(dragHome);\n        }\n    };\n\n    /**\n     * Clone drag item.\n     *\n     * @param {jQuery} dragHome\n     */\n    DragDropOntoImageQuestion.prototype.cloneDrag = function(dragHome) {\n        var drag = dragHome.clone();\n        drag.removeClass('draghome')\n            .addClass('drag unplaced moodle-has-zindex')\n            .offset(dragHome.offset());\n        this.getRoot().find('.dragitems').append(drag);\n    };\n\n    /**\n     * Update the position of drags.\n     */\n    DragDropOntoImageQuestion.prototype.positionDragsAndDrops = function() {\n        var thisQ = this,\n            root = this.getRoot(),\n            bgRatio = this.bgRatio();\n\n        // Move the drops into position.\n        root.find('.ddarea .dropzone').each(function(i, dropNode) {\n            var drop = $(dropNode),\n                place = thisQ.places[thisQ.getPlace(drop)];\n            // The xy values come from PHP as strings, so we need parseInt to stop JS doing string concatenation.\n            drop.css('left', parseInt(place.xy[0]) * bgRatio)\n                .css('top', parseInt(place.xy[1]) * bgRatio);\n            drop.data('originX', parseInt(place.xy[0]))\n                .data('originY', parseInt(place.xy[1]));\n            thisQ.handleElementScale(drop, 'left top');\n        });\n\n        // First move all items back home.\n        root.find('.draghome').not('.dragplaceholder').each(function(i, dragNode) {\n            var drag = $(dragNode),\n                currentPlace = thisQ.getClassnameNumericSuffix(drag, 'inplace');\n            drag.addClass('unplaced')\n                .removeClass('placed');\n            drag.removeAttr('tabindex');\n            if (currentPlace !== null) {\n                drag.removeClass('inplace' + currentPlace);\n            }\n        });\n\n        // Then place the ones that should be placed.\n        root.find('input.placeinput').each(function(i, inputNode) {\n            var input = $(inputNode),\n                choice = input.val();\n            if (choice.length === 0 || (choice.length > 0 && choice === '0')) {\n                // No item in this place.\n                return;\n            }\n\n            var place = thisQ.getPlace(input);\n            // Get the unplaced drag.\n            var unplacedDrag = thisQ.getUnplacedChoice(thisQ.getGroup(input), choice);\n            // Get the clone of the drag.\n            var hiddenDrag = thisQ.getDragClone(unplacedDrag);\n            if (hiddenDrag.length) {\n                if (unplacedDrag.hasClass('infinite')) {\n                    var noOfDrags = thisQ.noOfDropsInGroup(thisQ.getGroup(unplacedDrag));\n                    var cloneDrags = thisQ.getInfiniteDragClones(unplacedDrag, false);\n                    if (cloneDrags.length < noOfDrags) {\n                        var cloneDrag = unplacedDrag.clone();\n                        cloneDrag.removeClass('beingdragged');\n                        cloneDrag.removeAttr('tabindex');\n                        hiddenDrag.after(cloneDrag);\n                        // Sometimes, for the question that has a lot of input groups and unlimited draggable items,\n                        // this 'clone' process takes longer than usual, so the questionManager.init() method\n                        // will not add the eventHandler for this cloned drag.\n                        // We need to make sure to add the eventHandler for the cloned drag too.\n                        questionManager.addEventHandlersToDrag(cloneDrag);\n                    } else {\n                        hiddenDrag.addClass('active');\n                    }\n                } else {\n                    hiddenDrag.addClass('active');\n                }\n            }\n\n            // Send the drag to drop.\n            var drop = root.find('.dropzone.place' + place);\n            thisQ.sendDragToDrop(unplacedDrag, drop);\n        });\n\n        // Save the question answer.\n        thisQ.questionAnswer = thisQ.getQuestionAnsweredValues();\n    };\n\n    /**\n     * Get the question answered values.\n     *\n     * @return {Object} Contain key-value with key is the input id and value is the input value.\n     */\n    DragDropOntoImageQuestion.prototype.getQuestionAnsweredValues = function() {\n        let result = {};\n        this.getRoot().find('input.placeinput').each((i, inputNode) => {\n            result[inputNode.id] = inputNode.value;\n        });\n\n        return result;\n    };\n\n    /**\n     * Check if the question is being interacted or not.\n     *\n     * @return {boolean} Return true if the user has changed the question-answer.\n     */\n    DragDropOntoImageQuestion.prototype.isQuestionInteracted = function() {\n        const oldAnswer = this.questionAnswer;\n        const newAnswer = this.getQuestionAnsweredValues();\n        let isInteracted = false;\n\n        // First, check both answers have the same structure or not.\n        if (JSON.stringify(newAnswer) !== JSON.stringify(oldAnswer)) {\n            isInteracted = true;\n            return isInteracted;\n        }\n        // Check the values.\n        Object.keys(newAnswer).forEach(key => {\n            if (newAnswer[key] !== oldAnswer[key]) {\n                isInteracted = true;\n            }\n        });\n\n        return isInteracted;\n    };\n\n    /**\n     * Handles the start of dragging an item.\n     *\n     * @param {Event} e the touch start or mouse down event.\n     */\n    DragDropOntoImageQuestion.prototype.handleDragStart = function(e) {\n        var thisQ = this,\n            drag = $(e.target).closest('.draghome'),\n            currentIndex = this.calculateZIndex(),\n            newIndex = currentIndex + 2;\n\n        var info = dragDrop.prepare(e);\n        if (!info.start || drag.hasClass('beingdragged')) {\n            return;\n        }\n\n        drag.addClass('beingdragged').css('transform', '').css('z-index', newIndex);\n        var currentPlace = this.getClassnameNumericSuffix(drag, 'inplace');\n        if (currentPlace !== null) {\n            this.setInputValue(currentPlace, 0);\n            drag.removeClass('inplace' + currentPlace);\n            var hiddenDrop = thisQ.getDrop(drag, currentPlace);\n            if (hiddenDrop.length) {\n                hiddenDrop.addClass('active');\n                drag.offset(hiddenDrop.offset());\n            }\n        } else {\n            var hiddenDrag = thisQ.getDragClone(drag);\n            if (hiddenDrag.length) {\n                if (drag.hasClass('infinite')) {\n                    var noOfDrags = this.noOfDropsInGroup(thisQ.getGroup(drag));\n                    var cloneDrags = this.getInfiniteDragClones(drag, false);\n                    if (cloneDrags.length < noOfDrags) {\n                        var cloneDrag = drag.clone();\n                        cloneDrag.removeClass('beingdragged');\n                        cloneDrag.removeAttr('tabindex');\n                        hiddenDrag.after(cloneDrag);\n                        questionManager.addEventHandlersToDrag(cloneDrag);\n                        drag.offset(cloneDrag.offset());\n                    } else {\n                        hiddenDrag.addClass('active');\n                        drag.offset(hiddenDrag.offset());\n                    }\n                } else {\n                    hiddenDrag.addClass('active');\n                    drag.offset(hiddenDrag.offset());\n                }\n            }\n        }\n\n        dragDrop.start(e, drag, function(x, y, drag) {\n            thisQ.dragMove(x, y, drag);\n        }, function(x, y, drag) {\n            thisQ.dragEnd(x, y, drag);\n        });\n    };\n\n    /**\n     * Called whenever the currently dragged items moves.\n     *\n     * @param {Number} pageX the x position.\n     * @param {Number} pageY the y position.\n     * @param {jQuery} drag the item being moved.\n     */\n    DragDropOntoImageQuestion.prototype.dragMove = function(pageX, pageY, drag) {\n        var thisQ = this,\n            highlighted = false;\n        this.getRoot().find('.dropzone.group' + this.getGroup(drag)).each(function(i, dropNode) {\n            var drop = $(dropNode);\n            if (thisQ.isPointInDrop(pageX, pageY, drop) && !highlighted) {\n                highlighted = true;\n                drop.addClass('valid-drag-over-drop');\n            } else {\n                drop.removeClass('valid-drag-over-drop');\n            }\n        });\n        this.getRoot().find('.draghome.placed.group' + this.getGroup(drag)).not('.beingdragged').each(function(i, dropNode) {\n            var drop = $(dropNode);\n            if (thisQ.isPointInDrop(pageX, pageY, drop) && !highlighted && !thisQ.isDragSameAsDrop(drag, drop)) {\n                highlighted = true;\n                drop.addClass('valid-drag-over-drop');\n            } else {\n                drop.removeClass('valid-drag-over-drop');\n            }\n        });\n    };\n\n    /**\n     * Called when user drops a drag item.\n     *\n     * @param {Number} pageX the x position.\n     * @param {Number} pageY the y position.\n     * @param {jQuery} drag the item being moved.\n     */\n    DragDropOntoImageQuestion.prototype.dragEnd = function(pageX, pageY, drag) {\n        var thisQ = this,\n            root = this.getRoot(),\n            placed = false;\n\n        // Looking for drag that was dropped on a dropzone.\n        root.find('.dropzone.group' + this.getGroup(drag)).each(function(i, dropNode) {\n            var drop = $(dropNode);\n            if (!thisQ.isPointInDrop(pageX, pageY, drop)) {\n                // Not this drop.\n                return true;\n            }\n\n            // Now put this drag into the drop.\n            drop.removeClass('valid-drag-over-drop');\n            thisQ.sendDragToDrop(drag, drop);\n            placed = true;\n            return false; // Stop the each() here.\n        });\n\n        if (!placed) {\n            // Looking for drag that was dropped on a placed drag.\n            root.find('.draghome.placed.group' + this.getGroup(drag)).not('.beingdragged').each(function(i, placedNode) {\n                var placedDrag = $(placedNode);\n                if (!thisQ.isPointInDrop(pageX, pageY, placedDrag) || thisQ.isDragSameAsDrop(drag, placedDrag)) {\n                    // Not this placed drag.\n                    return true;\n                }\n\n                // Now put this drag into the drop.\n                placedDrag.removeClass('valid-drag-over-drop');\n                var currentPlace = thisQ.getClassnameNumericSuffix(placedDrag, 'inplace');\n                var drop = thisQ.getDrop(drag, currentPlace);\n                thisQ.sendDragToDrop(drag, drop);\n                placed = true;\n                return false; // Stop the each() here.\n            });\n        }\n\n        if (!placed) {\n            this.sendDragHome(drag);\n        }\n    };\n\n    /**\n     * Animate a drag item into a given place (or back home).\n     *\n     * @param {jQuery|null} drag the item to place. If null, clear the place.\n     * @param {jQuery} drop the place to put it.\n     */\n    DragDropOntoImageQuestion.prototype.sendDragToDrop = function(drag, drop) {\n        // Is there already a drag in this drop? if so, evict it.\n        var oldDrag = this.getCurrentDragInPlace(this.getPlace(drop));\n        if (oldDrag.length !== 0) {\n            oldDrag.addClass('beingdragged');\n            oldDrag.offset(oldDrag.offset());\n            var currentPlace = this.getClassnameNumericSuffix(oldDrag, 'inplace');\n            var hiddenDrop = this.getDrop(oldDrag, currentPlace);\n            hiddenDrop.addClass('active');\n            this.sendDragHome(oldDrag);\n        }\n\n        if (drag.length === 0) {\n            this.setInputValue(this.getPlace(drop), 0);\n            if (drop.data('isfocus')) {\n                drop.focus();\n            }\n        } else {\n            this.setInputValue(this.getPlace(drop), this.getChoice(drag));\n            drag.removeClass('unplaced')\n                .addClass('placed inplace' + this.getPlace(drop));\n            drag.attr('tabindex', 0);\n            this.animateTo(drag, drop);\n        }\n    };\n\n    /**\n     * Animate a drag back to its home.\n     *\n     * @param {jQuery} drag the item being moved.\n     */\n    DragDropOntoImageQuestion.prototype.sendDragHome = function(drag) {\n        var currentPlace = this.getClassnameNumericSuffix(drag, 'inplace');\n        if (currentPlace !== null) {\n            drag.removeClass('inplace' + currentPlace);\n        }\n        drag.data('unplaced', true);\n\n        this.animateTo(drag, this.getDragHome(this.getGroup(drag), this.getChoice(drag)));\n    };\n\n    /**\n     * Handles keyboard events on drops.\n     *\n     * Drops are focusable. Once focused, right/down/space switches to the next choice, and\n     * left/up switches to the previous. Escape clear.\n     *\n     * @param {KeyboardEvent} e\n     */\n    DragDropOntoImageQuestion.prototype.handleKeyPress = function(e) {\n        var drop = $(e.target).closest('.dropzone');\n        if (drop.length === 0) {\n            var placedDrag = $(e.target);\n            var currentPlace = this.getClassnameNumericSuffix(placedDrag, 'inplace');\n            if (currentPlace !== null) {\n                drop = this.getDrop(placedDrag, currentPlace);\n            }\n        }\n        var currentDrag = this.getCurrentDragInPlace(this.getPlace(drop)),\n            nextDrag = $();\n\n        switch (e.keyCode) {\n            case keys.space:\n            case keys.arrowRight:\n            case keys.arrowDown:\n                nextDrag = this.getNextDrag(this.getGroup(drop), currentDrag);\n                break;\n\n            case keys.arrowLeft:\n            case keys.arrowUp:\n                nextDrag = this.getPreviousDrag(this.getGroup(drop), currentDrag);\n                break;\n\n            case keys.escape:\n                questionManager.isKeyboardNavigation = false;\n                break;\n\n            default:\n                questionManager.isKeyboardNavigation = false;\n                return; // To avoid the preventDefault below.\n        }\n\n        if (nextDrag.length) {\n            nextDrag.data('isfocus', true);\n            nextDrag.addClass('beingdragged');\n            var hiddenDrag = this.getDragClone(nextDrag);\n            if (hiddenDrag.length) {\n                if (nextDrag.hasClass('infinite')) {\n                    var noOfDrags = this.noOfDropsInGroup(this.getGroup(nextDrag));\n                    var cloneDrags = this.getInfiniteDragClones(nextDrag, false);\n                    if (cloneDrags.length < noOfDrags) {\n                        var cloneDrag = nextDrag.clone();\n                        cloneDrag.removeClass('beingdragged');\n                        cloneDrag.removeAttr('tabindex');\n                        hiddenDrag.after(cloneDrag);\n                        questionManager.addEventHandlersToDrag(cloneDrag);\n                        nextDrag.offset(cloneDrag.offset());\n                    } else {\n                        hiddenDrag.addClass('active');\n                        nextDrag.offset(hiddenDrag.offset());\n                    }\n                } else {\n                    hiddenDrag.addClass('active');\n                    nextDrag.offset(hiddenDrag.offset());\n                }\n            }\n        } else {\n            drop.data('isfocus', true);\n        }\n\n        e.preventDefault();\n        this.sendDragToDrop(nextDrag, drop);\n    };\n\n    /**\n     * Choose the next drag in a group.\n     *\n     * @param {int} group which group.\n     * @param {jQuery} drag current choice (empty jQuery if there isn't one).\n     * @return {jQuery} the next drag in that group, or null if there wasn't one.\n     */\n    DragDropOntoImageQuestion.prototype.getNextDrag = function(group, drag) {\n        var choice,\n            numChoices = this.noOfChoicesInGroup(group);\n\n        if (drag.length === 0) {\n            choice = 1; // Was empty, so we want to select the first choice.\n        } else {\n            choice = this.getChoice(drag) + 1;\n        }\n\n        var next = this.getUnplacedChoice(group, choice);\n        while (next.length === 0 && choice < numChoices) {\n            choice++;\n            next = this.getUnplacedChoice(group, choice);\n        }\n\n        return next;\n    };\n\n    /**\n     * Choose the previous drag in a group.\n     *\n     * @param {int} group which group.\n     * @param {jQuery} drag current choice (empty jQuery if there isn't one).\n     * @return {jQuery} the next drag in that group, or null if there wasn't one.\n     */\n    DragDropOntoImageQuestion.prototype.getPreviousDrag = function(group, drag) {\n        var choice;\n\n        if (drag.length === 0) {\n            choice = this.noOfChoicesInGroup(group);\n        } else {\n            choice = this.getChoice(drag) - 1;\n        }\n\n        var previous = this.getUnplacedChoice(group, choice);\n        while (previous.length === 0 && choice > 1) {\n            choice--;\n            previous = this.getUnplacedChoice(group, choice);\n        }\n\n        // Does this choice exist?\n        return previous;\n    };\n\n    /**\n     * Animate an object to the given destination.\n     *\n     * @param {jQuery} drag the element to be animated.\n     * @param {jQuery} target element marking the place to move it to.\n     */\n    DragDropOntoImageQuestion.prototype.animateTo = function(drag, target) {\n        var currentPos = drag.offset(),\n            targetPos = target.offset(),\n            thisQ = this;\n\n        M.util.js_pending('qtype_ddimageortext-animate-' + thisQ.containerId);\n        // Animate works in terms of CSS position, whereas locating an object\n        // on the page works best with jQuery offset() function. So, to get\n        // the right target position, we work out the required change in\n        // offset() and then add that to the current CSS position.\n        drag.animate(\n            {\n                left: parseInt(drag.css('left')) + targetPos.left - currentPos.left,\n                top: parseInt(drag.css('top')) + targetPos.top - currentPos.top\n            },\n            {\n                duration: 'fast',\n                done: function() {\n                    $('body').trigger('qtype_ddimageortext-dragmoved', [drag, target, thisQ]);\n                    M.util.js_complete('qtype_ddimageortext-animate-' + thisQ.containerId);\n                }\n            }\n        );\n    };\n\n    /**\n     * Detect if a point is inside a given DOM node.\n     *\n     * @param {Number} pageX the x position.\n     * @param {Number} pageY the y position.\n     * @param {jQuery} drop the node to check (typically a drop).\n     * @return {boolean} whether the point is inside the node.\n     */\n    DragDropOntoImageQuestion.prototype.isPointInDrop = function(pageX, pageY, drop) {\n        var position = drop.offset();\n        if (drop.hasClass('draghome')) {\n            return pageX >= position.left && pageX < position.left + drop.outerWidth()\n                && pageY >= position.top && pageY < position.top + drop.outerHeight();\n        }\n        return pageX >= position.left && pageX < position.left + drop.width()\n            && pageY >= position.top && pageY < position.top + drop.height();\n    };\n\n    /**\n     * Set the value of the hidden input for a place, to record what is currently there.\n     *\n     * @param {int} place which place to set the input value for.\n     * @param {int} choice the value to set.\n     */\n    DragDropOntoImageQuestion.prototype.setInputValue = function(place, choice) {\n        this.getRoot().find('input.placeinput.place' + place).val(choice);\n    };\n\n    /**\n     * Get the outer div for this question.\n     *\n     * @returns {jQuery} containing that div.\n     */\n    DragDropOntoImageQuestion.prototype.getRoot = function() {\n        return $(document.getElementById(this.containerId));\n    };\n\n    /**\n     * Get the img that is the background image.\n     * @returns {jQuery} containing that img.\n     */\n    DragDropOntoImageQuestion.prototype.bgImage = function() {\n        return this.getRoot().find('img.dropbackground');\n    };\n\n    /**\n     * Get drag home for a given choice.\n     *\n     * @param {int} group the group.\n     * @param {int} choice the choice number.\n     * @returns {jQuery} containing that div.\n     */\n    DragDropOntoImageQuestion.prototype.getDragHome = function(group, choice) {\n        if (!this.getRoot().find('.draghome.dragplaceholder.group' + group + '.choice' + choice).is(':visible')) {\n            return this.getRoot().find('.dragitemgroup' + group +\n                ' .draghome.infinite' +\n                '.choice' + choice +\n                '.group' + group);\n        }\n        return this.getRoot().find('.draghome.dragplaceholder.group' + group + '.choice' + choice);\n    };\n\n    /**\n     * Get an unplaced choice for a particular group.\n     *\n     * @param {int} group the group.\n     * @param {int} choice the choice number.\n     * @returns {jQuery} jQuery wrapping the unplaced choice. If there isn't one, the jQuery will be empty.\n     */\n    DragDropOntoImageQuestion.prototype.getUnplacedChoice = function(group, choice) {\n        return this.getRoot().find('.ddarea .draghome.group' + group + '.choice' + choice + '.unplaced').slice(0, 1);\n    };\n\n    /**\n     * Get the drag that is currently in a given place.\n     *\n     * @param {int} place the place number.\n     * @return {jQuery} the current drag (or an empty jQuery if none).\n     */\n    DragDropOntoImageQuestion.prototype.getCurrentDragInPlace = function(place) {\n        return this.getRoot().find('.ddarea .draghome.inplace' + place);\n    };\n\n    /**\n     * Return the number of blanks in a given group.\n     *\n     * @param {int} group the group number.\n     * @returns {int} the number of drops.\n     */\n    DragDropOntoImageQuestion.prototype.noOfDropsInGroup = function(group) {\n        return this.getRoot().find('.dropzone.group' + group).length;\n    };\n\n    /**\n     * Return the number of choices in a given group.\n     *\n     * @param {int} group the group number.\n     * @returns {int} the number of choices.\n     */\n    DragDropOntoImageQuestion.prototype.noOfChoicesInGroup = function(group) {\n        return this.getRoot().find('.dragitemgroup' + group + ' .draghome').length;\n    };\n\n    /**\n     * Return the number at the end of the CSS class name with the given prefix.\n     *\n     * @param {jQuery} node\n     * @param {String} prefix name prefix\n     * @returns {Number|null} the suffix if found, else null.\n     */\n    DragDropOntoImageQuestion.prototype.getClassnameNumericSuffix = function(node, prefix) {\n        var classes = node.attr('class');\n        if (classes !== '') {\n            var classesArr = classes.split(' ');\n            for (var index = 0; index < classesArr.length; index++) {\n                var patt1 = new RegExp('^' + prefix + '([0-9])+$');\n                if (patt1.test(classesArr[index])) {\n                    var patt2 = new RegExp('([0-9])+$');\n                    var match = patt2.exec(classesArr[index]);\n                    return Number(match[0]);\n                }\n            }\n        }\n        return null;\n    };\n\n    /**\n     * Get the choice number of a drag.\n     *\n     * @param {jQuery} drag the drag.\n     * @returns {Number} the choice number.\n     */\n    DragDropOntoImageQuestion.prototype.getChoice = function(drag) {\n        return this.getClassnameNumericSuffix(drag, 'choice');\n    };\n\n    /**\n     * Given a DOM node that is significant to this question\n     * (drag, drop, ...) get the group it belongs to.\n     *\n     * @param {jQuery} node a DOM node.\n     * @returns {Number} the group it belongs to.\n     */\n    DragDropOntoImageQuestion.prototype.getGroup = function(node) {\n        return this.getClassnameNumericSuffix(node, 'group');\n    };\n\n    /**\n     * Get the place number of a drop, or its corresponding hidden input.\n     *\n     * @param {jQuery} node the DOM node.\n     * @returns {Number} the place number.\n     */\n    DragDropOntoImageQuestion.prototype.getPlace = function(node) {\n        return this.getClassnameNumericSuffix(node, 'place');\n    };\n\n    /**\n     * Get drag clone for a given drag.\n     *\n     * @param {jQuery} drag the drag.\n     * @returns {jQuery} the drag's clone.\n     */\n    DragDropOntoImageQuestion.prototype.getDragClone = function(drag) {\n        return this.getRoot().find('.dragitemgroup' +\n            this.getGroup(drag) +\n            ' .draghome' +\n            '.choice' + this.getChoice(drag) +\n            '.group' + this.getGroup(drag) +\n            '.dragplaceholder');\n    };\n\n    /**\n     * Get infinite drag clones for given drag.\n     *\n     * @param {jQuery} drag the drag.\n     * @param {Boolean} inHome in the home area or not.\n     * @returns {jQuery} the drag's clones.\n     */\n    DragDropOntoImageQuestion.prototype.getInfiniteDragClones = function(drag, inHome) {\n        if (inHome) {\n            return this.getRoot().find('.dragitemgroup' +\n                this.getGroup(drag) +\n                ' .draghome' +\n                '.choice' + this.getChoice(drag) +\n                '.group' + this.getGroup(drag) +\n                '.infinite').not('.dragplaceholder');\n        }\n        return this.getRoot().find('.draghome' +\n            '.choice' + this.getChoice(drag) +\n            '.group' + this.getGroup(drag) +\n            '.infinite').not('.dragplaceholder');\n    };\n\n    /**\n     * Get drop for a given drag and place.\n     *\n     * @param {jQuery} drag the drag.\n     * @param {Integer} currentPlace the current place of drag.\n     * @returns {jQuery} the drop's clone.\n     */\n    DragDropOntoImageQuestion.prototype.getDrop = function(drag, currentPlace) {\n        return this.getRoot().find('.dropzone.group' + this.getGroup(drag) + '.place' + currentPlace);\n    };\n\n    /**\n     * Handle when the window is resized.\n     */\n    DragDropOntoImageQuestion.prototype.handleResize = function() {\n        var thisQ = this,\n            bgRatio = this.bgRatio();\n        if (this.isPrinting) {\n            bgRatio = 1;\n        }\n\n        this.getRoot().find('.ddarea .dropzone').each(function(i, dropNode) {\n            $(dropNode)\n                .css('left', parseInt($(dropNode).data('originX')) * parseFloat(bgRatio))\n                .css('top', parseInt($(dropNode).data('originY')) * parseFloat(bgRatio));\n            thisQ.handleElementScale(dropNode, 'left top');\n        });\n\n        this.getRoot().find('div.droparea .draghome').not('.beingdragged').each(function(key, drag) {\n            $(drag)\n                .css('left', parseFloat($(drag).data('originX')) * parseFloat(bgRatio))\n                .css('top', parseFloat($(drag).data('originY')) * parseFloat(bgRatio));\n            thisQ.handleElementScale(drag, 'left top');\n        });\n    };\n\n    /**\n     * Return the background ratio.\n     *\n     * @returns {number} Background ratio.\n     */\n    DragDropOntoImageQuestion.prototype.bgRatio = function() {\n        var bgImg = this.bgImage();\n        var bgImgNaturalWidth = bgImg.get(0).naturalWidth;\n        var bgImgClientWidth = bgImg.width();\n\n        return bgImgClientWidth / bgImgNaturalWidth;\n    };\n\n    /**\n     * Scale the drag if needed.\n     *\n     * @param {jQuery} element the item to place.\n     * @param {String} type scaling type\n     */\n    DragDropOntoImageQuestion.prototype.handleElementScale = function(element, type) {\n        var bgRatio = parseFloat(this.bgRatio());\n        if (this.isPrinting) {\n            bgRatio = 1;\n        }\n        $(element).css({\n            '-webkit-transform': 'scale(' + bgRatio + ')',\n            '-moz-transform': 'scale(' + bgRatio + ')',\n            '-ms-transform': 'scale(' + bgRatio + ')',\n            '-o-transform': 'scale(' + bgRatio + ')',\n            'transform': 'scale(' + bgRatio + ')',\n            'transform-origin': type\n        });\n    };\n\n    /**\n     * Calculate z-index value.\n     *\n     * @returns {number} z-index value\n     */\n    DragDropOntoImageQuestion.prototype.calculateZIndex = function() {\n        var zIndex = 0;\n        this.getRoot().find('.ddarea .dropzone, div.droparea .draghome').each(function(i, dropNode) {\n            dropNode = $(dropNode);\n            // Note that webkit browsers won't return the z-index value from the CSS stylesheet\n            // if the element doesn't have a position specified. Instead it'll return \"auto\".\n            var itemZIndex = dropNode.css('z-index') ? parseInt(dropNode.css('z-index')) : 0;\n\n            if (itemZIndex > zIndex) {\n                zIndex = itemZIndex;\n            }\n        });\n\n        return zIndex;\n    };\n\n    /**\n     * Check that the drag is drop to it's clone.\n     *\n     * @param {jQuery} drag The drag.\n     * @param {jQuery} drop The drop.\n     * @returns {boolean}\n     */\n    DragDropOntoImageQuestion.prototype.isDragSameAsDrop = function(drag, drop) {\n        return this.getChoice(drag) === this.getChoice(drop) && this.getGroup(drag) === this.getGroup(drop);\n    };\n\n    /**\n     * Singleton object that handles all the DragDropOntoImageQuestions\n     * on the page, and deals with event dispatching.\n     * @type {Object}\n     */\n    var questionManager = {\n\n        /**\n         * {boolean} ensures that the event handlers are only initialised once per page.\n         */\n        eventHandlersInitialised: false,\n\n        /**\n         * {Object} ensures that the drag event handlers are only initialised once per question,\n         * indexed by containerId (id on the .que div).\n         */\n        dragEventHandlersInitialised: {},\n\n        /**\n         * {boolean} is printing or not.\n         */\n        isPrinting: false,\n\n        /**\n         * {boolean} is keyboard navigation or not.\n         */\n        isKeyboardNavigation: false,\n\n        /**\n         * {Object} all the questions on this page, indexed by containerId (id on the .que div).\n         */\n        questions: {}, // An object containing all the information about each question on the page.\n\n        /**\n         * Initialise one question.\n         *\n         * @method\n         * @param {String} containerId the id of the div.que that contains this question.\n         * @param {boolean} readOnly whether the question is read-only.\n         * @param {Array} places data.\n         */\n        init: function(containerId, readOnly, places) {\n            questionManager.questions[containerId] =\n                new DragDropOntoImageQuestion(containerId, readOnly, places);\n            if (!questionManager.eventHandlersInitialised) {\n                questionManager.setupEventHandlers();\n                questionManager.eventHandlersInitialised = true;\n            }\n            if (!questionManager.dragEventHandlersInitialised.hasOwnProperty(containerId)) {\n                questionManager.dragEventHandlersInitialised[containerId] = true;\n                // We do not use the body event here to prevent the other event on Mobile device, such as scroll event.\n                var questionContainer = document.getElementById(containerId);\n                if (questionContainer.classList.contains('ddimageortext') &&\n                    !questionContainer.classList.contains('qtype_ddimageortext-readonly')) {\n                    // TODO: Convert all the jQuery selectors and events to native Javascript.\n                    questionManager.addEventHandlersToDrag($(questionContainer).find('.draghome'));\n                }\n            }\n        },\n\n        /**\n         * Set up the event handlers that make this question type work. (Done once per page.)\n         */\n        setupEventHandlers: function() {\n            $('body')\n                .on('keydown',\n                    '.que.ddimageortext:not(.qtype_ddimageortext-readonly) .dropzones .dropzone',\n                    questionManager.handleKeyPress)\n                .on('keydown',\n                    '.que.ddimageortext:not(.qtype_ddimageortext-readonly) .draghome.placed:not(.beingdragged)',\n                    questionManager.handleKeyPress)\n                .on('qtype_ddimageortext-dragmoved', questionManager.handleDragMoved);\n            $(window).on('resize', function() {\n                questionManager.handleWindowResize(false);\n            });\n            window.addEventListener('beforeprint', function() {\n                questionManager.isPrinting = true;\n                questionManager.handleWindowResize(questionManager.isPrinting);\n            });\n            window.addEventListener('afterprint', function() {\n                questionManager.isPrinting = false;\n                questionManager.handleWindowResize(questionManager.isPrinting);\n            });\n            setTimeout(function() {\n                questionManager.fixLayoutIfThingsMoved();\n            }, 100);\n        },\n\n        /**\n         * Binding the drag/touch event again for newly created element.\n         *\n         * @param {jQuery} element Element to bind the event\n         */\n        addEventHandlersToDrag: function(element) {\n            // Unbind all the mousedown and touchstart events to prevent double binding.\n            element.unbind('mousedown touchstart');\n            element.on('mousedown touchstart', questionManager.handleDragStart);\n        },\n\n        /**\n         * Handle mouse down / touch start events on drags.\n         * @param {Event} e the DOM event.\n         */\n        handleDragStart: function(e) {\n            e.preventDefault();\n            var question = questionManager.getQuestionForEvent(e);\n            if (question) {\n                question.handleDragStart(e);\n            }\n        },\n\n        /**\n         * Handle key down / press events on drags.\n         * @param {KeyboardEvent} e\n         */\n        handleKeyPress: function(e) {\n            if (questionManager.isKeyboardNavigation) {\n                return;\n            }\n            questionManager.isKeyboardNavigation = true;\n            var question = questionManager.getQuestionForEvent(e);\n            if (question) {\n                question.handleKeyPress(e);\n            }\n        },\n\n        /**\n         * Handle when the window is resized.\n         * @param {boolean} isPrinting\n         */\n        handleWindowResize: function(isPrinting) {\n            for (var containerId in questionManager.questions) {\n                if (questionManager.questions.hasOwnProperty(containerId)) {\n                    questionManager.questions[containerId].isPrinting = isPrinting;\n                    questionManager.questions[containerId].handleResize();\n                }\n            }\n        },\n\n        /**\n         * Sometimes, despite our best efforts, things change in a way that cannot\n         * be specifically caught (e.g. dock expanding or collapsing in Boost).\n         * Therefore, we need to periodically check everything is in the right position.\n         */\n        fixLayoutIfThingsMoved: function() {\n            this.handleWindowResize(questionManager.isPrinting);\n            // We use setTimeout after finishing work, rather than setInterval,\n            // in case positioning things is slow. We want 100 ms gap\n            // between executions, not what setInterval does.\n            setTimeout(function() {\n                questionManager.fixLayoutIfThingsMoved(questionManager.isPrinting);\n            }, 100);\n        },\n\n        /**\n         * Handle when drag moved.\n         *\n         * @param {Event} e the event.\n         * @param {jQuery} drag the drag\n         * @param {jQuery} target the target\n         * @param {DragDropOntoImageQuestion} thisQ the question.\n         */\n        handleDragMoved: function(e, drag, target, thisQ) {\n            drag.removeClass('beingdragged').css('z-index', '');\n            drag.css('top', target.position().top).css('left', target.position().left);\n            target.after(drag);\n            target.removeClass('active');\n            if (typeof drag.data('unplaced') !== 'undefined' && drag.data('unplaced') === true) {\n                drag.removeClass('placed').addClass('unplaced');\n                drag.removeAttr('tabindex');\n                drag.removeData('unplaced');\n                drag.css('top', '')\n                    .css('left', '')\n                    .css('transform', '');\n                if (drag.hasClass('infinite') && thisQ.getInfiniteDragClones(drag, true).length > 1) {\n                    thisQ.getInfiniteDragClones(drag, true).first().remove();\n                }\n            } else {\n                drag.data('originX', target.data('originX')).data('originY', target.data('originY'));\n                thisQ.handleElementScale(drag, 'left top');\n            }\n            if (typeof drag.data('isfocus') !== 'undefined' && drag.data('isfocus') === true) {\n                drag.focus();\n                drag.removeData('isfocus');\n            }\n            if (typeof target.data('isfocus') !== 'undefined' && target.data('isfocus') === true) {\n                target.removeData('isfocus');\n            }\n            if (questionManager.isKeyboardNavigation) {\n                questionManager.isKeyboardNavigation = false;\n            }\n            if (thisQ.isQuestionInteracted()) {\n                // The user has interacted with the draggable items. We need to mark the form as dirty.\n                questionManager.handleFormDirty();\n                // Save the new answered value.\n                thisQ.questionAnswer = thisQ.getQuestionAnsweredValues();\n            }\n        },\n\n        /**\n         * Given an event, work out which question it effects.\n         * @param {Event} e the event.\n         * @returns {DragDropOntoImageQuestion|undefined} The question, or undefined.\n         */\n        getQuestionForEvent: function(e) {\n            var containerId = $(e.currentTarget).closest('.que.ddimageortext').attr('id');\n            return questionManager.questions[containerId];\n        },\n\n        /**\n         * Handle when the form is dirty.\n         */\n        handleFormDirty: function() {\n            const responseForm = document.getElementById('responseform');\n            FormChangeChecker.markFormAsDirty(responseForm);\n        }\n    };\n\n    /**\n     * @alias module:qtype_ddimageortext/question\n     */\n    return {\n        init: questionManager.init\n    };\n});\n"], "names": ["define", "$", "dragDrop", "keys", "FormChangeChecker", "filterEvent", "DragDropOntoImageQuestion", "containerId", "readOnly", "places", "questionAnswer", "questionDragDropWidthHeight", "M", "util", "js_pending", "this", "allImagesLoaded", "imageLoadingTimeoutId", "isPrinting", "getRoot", "addClass", "thisQ", "getNotYetLoadedImages", "one", "waitForAllImagesToBeLoaded", "prototype", "changeAllDragsAndDropsToFilteredContent", "filteredElement", "currentFilteredItem", "parentIsDD", "parent", "closest", "hasClass", "isDD", "find", "length", "group", "getGroup", "choice", "getChoice", "listOfModifiedDragDrop", "each", "i", "node", "get", "originalClass", "attr", "originalStyle", "filteredDragDropClone", "clone", "questionManager", "addEventHandlersToDrag", "before", "push", "for<PERSON>ach", "remove", "currentHeight", "height", "currentWidth", "width", "offsetWidth", "offsetHeight", "classList", "add", "max<PERSON><PERSON><PERSON>", "Math", "ceil", "maxHeight", "resizeAllDragsAndDropsInGroup", "top", "floor", "css", "clearTimeout", "setTimeout", "setupQuestion", "document", "addEventListener", "eventTypes", "filterContentRenderingComplete", "elements", "detail", "nodes", "element", "not", "imgNode", "imageIsLoaded", "imgElement", "complete", "naturalHeight", "resizeAllDragsAndDrops", "cloneDrags", "positionDragsAndDrops", "js_complete", "getClassnameNumericSuffix", "root", "dragHomes", "drag", "max", "hasOwnProperty", "place", "label", "text", "parseInt", "get_string", "append", "index", "dragHome", "placeHolder", "removeClass", "cloneDragsForOneChoice", "noOfDrags", "noOfDropsInGroup", "cloneDrag", "offset", "bgRatio", "dropNode", "drop", "getPlace", "xy", "data", "handleElementScale", "dragNode", "currentPlace", "removeAttr", "inputNode", "input", "val", "unplacedDrag", "getUnplacedChoice", "hiddenDrag", "getDragClone", "getInfiniteDragClones", "after", "sendDragToDrop", "getQuestionAnsweredValues", "result", "id", "value", "isQuestionInteracted", "oldAnswer", "newAnswer", "isInteracted", "JSON", "stringify", "Object", "key", "handleDragStart", "e", "target", "newIndex", "calculateZIndex", "prepare", "start", "setInputValue", "hiddenDrop", "getDrop", "x", "y", "dragMove", "dragEnd", "pageX", "pageY", "highlighted", "isPointInDrop", "isDragSameAsDrop", "placed", "placedNode", "placedDrag", "sendDragHome", "oldDrag", "getCurrentDragInPlace", "focus", "animateTo", "getDragHome", "handleKeyPress", "currentDrag", "nextDrag", "keyCode", "space", "arrowRight", "arrowDown", "getNextDrag", "arrowLeft", "arrowUp", "getPreviousDrag", "escape", "isKeyboardNavigation", "preventDefault", "numChoices", "noOfChoicesInGroup", "next", "previous", "currentPos", "targetPos", "animate", "left", "duration", "done", "trigger", "position", "outerWidth", "outerHeight", "getElementById", "bgImage", "is", "slice", "prefix", "classes", "classesArr", "split", "RegExp", "test", "match", "exec", "Number", "inHome", "handleResize", "parseFloat", "bgImg", "bgImgNaturalWidth", "naturalWidth", "type", "zIndex", "itemZIndex", "eventHandlersInitialised", "dragEventHandlersInitialised", "questions", "init", "setupEventHandlers", "<PERSON><PERSON><PERSON><PERSON>", "contains", "on", "handleDragMoved", "window", "handleWindowResize", "fixLayoutIfThingsMoved", "unbind", "question", "getQuestionForEvent", "removeData", "first", "handleFormDirty", "currentTarget", "responseForm", "mark<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;AAsBAA,sCAAO,CACH,SACA,gBACA,iBACA,0BACA,wBACD,SACCC,EACAC,SACAC,KACAC,kBACAC,sBAaSC,0BAA0BC,YAAaC,SAAUC,aACjDF,YAAcA,iBACdG,eAAiB,QACjBC,4BAA8B,GACnCC,EAAEC,KAAKC,WAAW,4BAA8BC,KAAKR,kBAChDE,OAASA,YACTO,iBAAkB,OAClBC,sBAAwB,UACxBC,YAAa,EACdV,eACKW,UAAUC,SAAS,oCAGxBC,MAAQN,UACPO,wBAAwBC,IAAI,QAAQ,WACrCF,MAAMG,qCAELA,6BAQTlB,0BAA0BmB,UAAUC,wCAA0C,SAASC,qBAC/EC,oBAAsB3B,EAAE0B,uBACtBE,WAAaD,oBAAoBE,SAASC,QAAQ,OAAOC,SAAS,WACpEJ,oBAAoBE,SAASE,SAAS,YACpCC,KAAOL,oBAAoBI,SAAS,WAAaJ,oBAAoBI,SAAS,gBAE/EH,aAAeI,eAGhBJ,aACAD,oBAAsBA,oBAAoBE,SAASC,QAAQ,QAE3DhB,KAAKI,UAAUe,KAAKN,qBAAqBO,QAAU,eAKjDC,MAAQrB,KAAKsB,SAAST,qBACxBU,OAASvB,KAAKwB,UAAUX,yBACxBY,uBAAyB,QAExBrB,UAAUe,KAAK,SAAWE,MAAQ,UAAYE,QAAQG,MAAK,SAASC,EAAGC,SAEpE1C,EAAE0C,MAAMC,IAAI,KAAOhB,oBAAoBgB,IAAI,gBAGzCC,cAAgB5C,EAAE0C,MAAMG,KAAK,SAC7BC,cAAgB9C,EAAE0C,MAAMG,KAAK,SAE7BE,sBAAwBpB,oBAAoBqB,QAIlDC,gBAAgBC,uBAAuBH,uBAEvCA,sBAAsBF,KAAK,QAASD,eACpCG,sBAAsBF,KAAK,QAASC,eAEpC9C,EAAE0C,MAAMS,OAAOJ,uBAEfR,uBAAuBa,KAAKV,SAGhCH,uBAAuBc,SAAQ,SAASX,MACpC1C,EAAE0C,MAAMY,kBAGNC,cAAgB5B,oBAAoB6B,SACpCC,aAAe9B,oBAAoB+B,WAEzC/B,oBAAoB6B,OAAO,QAC3B7B,oBAAoB+B,MAAM,QAGrBhC,gBAAgBiC,aAAgBjC,gBAAgBkC,cACjDlC,gBAAgBmC,UAAUC,IAAI,WAE9BhD,KAAKJ,4BAA4ByB,OAAO4B,SAAWC,KAAKC,KAAKvC,gBAAgBiC,cAC7E7C,KAAKJ,4BAA4ByB,OAAO+B,UAAYF,KAAKC,KAAK,EAAIvC,gBAAgBkC,cAElFlC,gBAAgBmC,UAAUP,OAAO,gBAE5Ba,8BAA8BhC,WAChC,OAEGiC,IAAMJ,KAAKK,OAAOvD,KAAKJ,4BAA4ByB,OAAO+B,UAAYxC,gBAAgBkC,cAAgB,GAE5GjC,oBAAoB+B,MAAMD,cAAcD,OAAOD,eAAee,IAAI,eAC/CF,IAAM,OAI7B1C,gBAAgBmC,UAAUP,OAAO,YASrCjD,0BAA0BmB,UAAUD,2BAA6B,eACzDH,MAAQN,KAIRA,KAAKC,kBAK0B,OAA/BD,KAAKE,uBACLuD,aAAazD,KAAKE,uBAMlBF,KAAKO,wBAAwBa,OAAS,OACjClB,sBAAwBwD,YAAW,WACpCpD,MAAMG,+BACP,WAKFR,iBAAkB,EACvBK,MAAMqD,gBAENC,SAASC,iBAAiBvE,YAAYwE,WAAWC,gCAAiCC,WAC9EA,SAASC,OAAOC,MAAM3B,SAAS4B,UAC3B7D,MAAMK,wCAAwCwD,kBAU1D5E,0BAA0BmB,UAAUH,sBAAwB,eACpDD,MAAQN,YACLA,KAAKI,UAAUe,KAAK,eAAeiD,KAAI,SAASzC,EAAG0C,gBAC/C/D,MAAMgE,cAAcD,aAUnC9E,0BAA0BmB,UAAU4D,cAAgB,SAASC,mBAClDA,WAAWC,UAAyC,IAA7BD,WAAWE,eAM7ClF,0BAA0BmB,UAAUiD,cAAgB,gBAC3Ce,8BACAC,kBACAC,wBACL/E,EAAEC,KAAK+E,YAAY,4BAA8B7E,KAAKR,cAM1DD,0BAA0BmB,UAAUgE,uBAAyB,eACrDpE,MAAQN,UACPI,UAAUe,KAAK,oBAAoBO,MAAK,SAASC,EAAGC,MACrDtB,MAAM+C,8BACF/C,MAAMwE,0BAA0B5F,EAAE0C,MAAO,sBASrDrC,0BAA0BmB,UAAU2C,8BAAgC,SAAShC,WACrE0D,KAAO/E,KAAKI,UACZ4E,UAAYD,KAAK5D,KAAK,kBAAoBE,OAC1C4B,SAAW,EACXG,UAAY,MAuBX,IAAIzB,KApBTqD,UAAUtD,MAAK,SAASC,EAAGsD,MACvBhC,SAAWC,KAAKgC,IAAIjC,SAAUC,KAAKC,KAAK8B,KAAKpC,cAC7CO,UAAYF,KAAKgC,IAAI9B,UAAWF,KAAKC,KAAK8B,KAAKnC,kBAInDG,UAAY,GACZG,WAAa,QACRxD,4BAA4ByB,OAAS,CAAC4B,SAAAA,SAAUG,UAAAA,WAGrD4B,UAAUtD,MAAK,SAASC,EAAGsD,YACjB3B,IAAMJ,KAAKK,OAAOH,UAAY6B,KAAKnC,cAAgB,GAEzD5D,EAAE+F,MAAMrC,MAAMK,UAAUP,OAAOU,WAAWI,IAAI,eAC3BF,IAAM,UAKftD,KAAKN,UACVM,KAAKN,OAAOyF,eAAgBxD,QAG7ByD,MAAQpF,KAAKN,OAAOiC,GACpB0D,MAAQD,MAAME,KACdC,SAASH,MAAM/D,SAAWA,QAGhB,KAAVgE,QACAA,MAAQxF,EAAEC,KAAK0F,WAAW,QAAS,wBAE6C,IAAhFT,KAAK5D,KAAK,6BAA+BiE,MAAM/D,MAAQ,SAAWM,GAAGP,QACrE2D,KAAK5D,KAAK,cAAcsE,OAAO,oCAAsCL,MAAM/D,MACvE,SAAWM,EADgB,2CAEG0D,MAAQ,uBAE9CN,KAAK5D,KAAK,kBAAoBQ,GAAGiB,MAAMK,SAAW,GAAGP,OAAOU,UAAY,MAShF7D,0BAA0BmB,UAAUiE,WAAa,eACzCrE,MAAQN,KACZM,MAAMF,UAAUe,KAAK,aAAaO,MAAK,SAASgE,MAAOC,cAC/CV,KAAO/F,EAAEyG,UACTC,YAAcX,KAAK/C,QACvB0D,YAAYC,cACZD,YAAYvF,SAAS,kBACjBC,MAAMkB,UAAUyD,MAAQ,SACxB3E,MAAMgB,SAAS2D,MAAQ,oBAC3BA,KAAK5C,OAAOuD,iBASpBrG,0BAA0BmB,UAAUoF,uBAAyB,SAASH,aAC9DA,SAAS1E,SAAS,oBACd8E,UAAY/F,KAAKgG,iBAAiBhG,KAAKsB,SAASqE,WAC3ChE,EAAI,EAAGA,EAAIoE,UAAWpE,SACtBsE,UAAUN,oBAGdM,UAAUN,WASvBpG,0BAA0BmB,UAAUuF,UAAY,SAASN,cACjDV,KAAOU,SAASzD,QACpB+C,KAAKY,YAAY,YACZxF,SAAS,mCACT6F,OAAOP,SAASO,eAChB9F,UAAUe,KAAK,cAAcsE,OAAOR,OAM7C1F,0BAA0BmB,UAAUkE,sBAAwB,eACpDtE,MAAQN,KACR+E,KAAO/E,KAAKI,UACZ+F,QAAUnG,KAAKmG,UAGnBpB,KAAK5D,KAAK,qBAAqBO,MAAK,SAASC,EAAGyE,cACxCC,KAAOnH,EAAEkH,UACThB,MAAQ9E,MAAMZ,OAAOY,MAAMgG,SAASD,OAExCA,KAAK7C,IAAI,OAAQ+B,SAASH,MAAMmB,GAAG,IAAMJ,SACpC3C,IAAI,MAAO+B,SAASH,MAAMmB,GAAG,IAAMJ,SACxCE,KAAKG,KAAK,UAAWjB,SAASH,MAAMmB,GAAG,KAClCC,KAAK,UAAWjB,SAASH,MAAMmB,GAAG,KACvCjG,MAAMmG,mBAAmBJ,KAAM,eAInCtB,KAAK5D,KAAK,aAAaiD,IAAI,oBAAoB1C,MAAK,SAASC,EAAG+E,cACxDzB,KAAO/F,EAAEwH,UACTC,aAAerG,MAAMwE,0BAA0BG,KAAM,WACzDA,KAAK5E,SAAS,YACTwF,YAAY,UACjBZ,KAAK2B,WAAW,YACK,OAAjBD,cACA1B,KAAKY,YAAY,UAAYc,iBAKrC5B,KAAK5D,KAAK,oBAAoBO,MAAK,SAASC,EAAGkF,eACvCC,MAAQ5H,EAAE2H,WACVtF,OAASuF,MAAMC,WACG,IAAlBxF,OAAOH,QAAiBG,OAAOH,OAAS,GAAgB,MAAXG,aAK7C6D,MAAQ9E,MAAMgG,SAASQ,OAEvBE,aAAe1G,MAAM2G,kBAAkB3G,MAAMgB,SAASwF,OAAQvF,QAE9D2F,WAAa5G,MAAM6G,aAAaH,iBAChCE,WAAW9F,UACP4F,aAAa/F,SAAS,YAAa,KAC/B8E,UAAYzF,MAAM0F,iBAAiB1F,MAAMgB,SAAS0F,kBACrC1G,MAAM8G,sBAAsBJ,cAAc,GAC5C5F,OAAS2E,UAAW,KAC3BE,UAAYe,aAAa9E,QAC7B+D,UAAUJ,YAAY,gBACtBI,UAAUW,WAAW,YACrBM,WAAWG,MAAMpB,WAKjB9D,gBAAgBC,uBAAuB6D,gBAEvCiB,WAAW7G,SAAS,eAGxB6G,WAAW7G,SAAS,cAKxBgG,KAAOtB,KAAK5D,KAAK,kBAAoBiE,OACzC9E,MAAMgH,eAAeN,aAAcX,UAIvC/F,MAAMX,eAAiBW,MAAMiH,6BAQjChI,0BAA0BmB,UAAU6G,0BAA4B,eACxDC,OAAS,eACRpH,UAAUe,KAAK,oBAAoBO,MAAK,CAACC,EAAGkF,aAC7CW,OAAOX,UAAUY,IAAMZ,UAAUa,SAG9BF,QAQXjI,0BAA0BmB,UAAUiH,qBAAuB,iBACjDC,UAAY5H,KAAKL,eACjBkI,UAAY7H,KAAKuH,gCACnBO,cAAe,SAGfC,KAAKC,UAAUH,aAAeE,KAAKC,UAAUJ,YAC7CE,cAAe,EACRA,eAGXG,OAAO7I,KAAKyI,WAAWtF,SAAQ2F,MACvBL,UAAUK,OAASN,UAAUM,OAC7BJ,cAAe,MAIhBA,eAQXvI,0BAA0BmB,UAAUyH,gBAAkB,SAASC,OACvD9H,MAAQN,KACRiF,KAAO/F,EAAEkJ,EAAEC,QAAQrH,QAAQ,aAE3BsH,SADetI,KAAKuI,kBACM,KAEnBpJ,SAASqJ,QAAQJ,GAClBK,QAASxD,KAAKhE,SAAS,iBAIjCgE,KAAK5E,SAAS,gBAAgBmD,IAAI,YAAa,IAAIA,IAAI,UAAW8E,cAC9D3B,aAAe3G,KAAK8E,0BAA0BG,KAAM,cACnC,OAAjB0B,aAAuB,MAClB+B,cAAc/B,aAAc,GACjC1B,KAAKY,YAAY,UAAYc,kBACzBgC,WAAarI,MAAMsI,QAAQ3D,KAAM0B,cACjCgC,WAAWvH,SACXuH,WAAWtI,SAAS,UACpB4E,KAAKiB,OAAOyC,WAAWzC,eAExB,KACCgB,WAAa5G,MAAM6G,aAAalC,SAChCiC,WAAW9F,UACP6D,KAAKhE,SAAS,YAAa,KACvB8E,UAAY/F,KAAKgG,iBAAiB1F,MAAMgB,SAAS2D,UACpCjF,KAAKoH,sBAAsBnC,MAAM,GACnC7D,OAAS2E,UAAW,KAC3BE,UAAYhB,KAAK/C,QACrB+D,UAAUJ,YAAY,gBACtBI,UAAUW,WAAW,YACrBM,WAAWG,MAAMpB,WACjB9D,gBAAgBC,uBAAuB6D,WACvChB,KAAKiB,OAAOD,UAAUC,eAEtBgB,WAAW7G,SAAS,UACpB4E,KAAKiB,OAAOgB,WAAWhB,eAG3BgB,WAAW7G,SAAS,UACpB4E,KAAKiB,OAAOgB,WAAWhB,UAKnC/G,SAASsJ,MAAML,EAAGnD,MAAM,SAAS4D,EAAGC,EAAG7D,MACnC3E,MAAMyI,SAASF,EAAGC,EAAG7D,SACtB,SAAS4D,EAAGC,EAAG7D,MACd3E,MAAM0I,QAAQH,EAAGC,EAAG7D,WAW5B1F,0BAA0BmB,UAAUqI,SAAW,SAASE,MAAOC,MAAOjE,UAC9D3E,MAAQN,KACRmJ,aAAc,OACb/I,UAAUe,KAAK,kBAAoBnB,KAAKsB,SAAS2D,OAAOvD,MAAK,SAASC,EAAGyE,cACtEC,KAAOnH,EAAEkH,UACT9F,MAAM8I,cAAcH,MAAOC,MAAO7C,QAAU8C,aAC5CA,aAAc,EACd9C,KAAKhG,SAAS,yBAEdgG,KAAKR,YAAY,gCAGpBzF,UAAUe,KAAK,yBAA2BnB,KAAKsB,SAAS2D,OAAOb,IAAI,iBAAiB1C,MAAK,SAASC,EAAGyE,cAClGC,KAAOnH,EAAEkH,WACT9F,MAAM8I,cAAcH,MAAOC,MAAO7C,OAAU8C,aAAgB7I,MAAM+I,iBAAiBpE,KAAMoB,MAIzFA,KAAKR,YAAY,yBAHjBsD,aAAc,EACd9C,KAAKhG,SAAS,6BAc1Bd,0BAA0BmB,UAAUsI,QAAU,SAASC,MAAOC,MAAOjE,UAC7D3E,MAAQN,KACR+E,KAAO/E,KAAKI,UACZkJ,QAAS,EAGbvE,KAAK5D,KAAK,kBAAoBnB,KAAKsB,SAAS2D,OAAOvD,MAAK,SAASC,EAAGyE,cAC5DC,KAAOnH,EAAEkH,iBACR9F,MAAM8I,cAAcH,MAAOC,MAAO7C,QAMvCA,KAAKR,YAAY,wBACjBvF,MAAMgH,eAAerC,KAAMoB,MAC3BiD,QAAS,GACF,MAGNA,QAEDvE,KAAK5D,KAAK,yBAA2BnB,KAAKsB,SAAS2D,OAAOb,IAAI,iBAAiB1C,MAAK,SAASC,EAAG4H,gBACxFC,WAAatK,EAAEqK,gBACdjJ,MAAM8I,cAAcH,MAAOC,MAAOM,aAAelJ,MAAM+I,iBAAiBpE,KAAMuE,mBAExE,EAIXA,WAAW3D,YAAY,4BACnBc,aAAerG,MAAMwE,0BAA0B0E,WAAY,WAC3DnD,KAAO/F,MAAMsI,QAAQ3D,KAAM0B,qBAC/BrG,MAAMgH,eAAerC,KAAMoB,MAC3BiD,QAAS,GACF,KAIVA,aACIG,aAAaxE,OAU1B1F,0BAA0BmB,UAAU4G,eAAiB,SAASrC,KAAMoB,UAE5DqD,QAAU1J,KAAK2J,sBAAsB3J,KAAKsG,SAASD,UAChC,IAAnBqD,QAAQtI,OAAc,CACtBsI,QAAQrJ,SAAS,gBACjBqJ,QAAQxD,OAAOwD,QAAQxD,cACnBS,aAAe3G,KAAK8E,0BAA0B4E,QAAS,WAC1C1J,KAAK4I,QAAQc,QAAS/C,cAC5BtG,SAAS,eACfoJ,aAAaC,SAGF,IAAhBzE,KAAK7D,aACAsH,cAAc1I,KAAKsG,SAASD,MAAO,GACpCA,KAAKG,KAAK,YACVH,KAAKuD,eAGJlB,cAAc1I,KAAKsG,SAASD,MAAOrG,KAAKwB,UAAUyD,OACvDA,KAAKY,YAAY,YACZxF,SAAS,iBAAmBL,KAAKsG,SAASD,OAC/CpB,KAAKlD,KAAK,WAAY,QACjB8H,UAAU5E,KAAMoB,QAS7B9G,0BAA0BmB,UAAU+I,aAAe,SAASxE,UACpD0B,aAAe3G,KAAK8E,0BAA0BG,KAAM,WACnC,OAAjB0B,cACA1B,KAAKY,YAAY,UAAYc,cAEjC1B,KAAKuB,KAAK,YAAY,QAEjBqD,UAAU5E,KAAMjF,KAAK8J,YAAY9J,KAAKsB,SAAS2D,MAAOjF,KAAKwB,UAAUyD,SAW9E1F,0BAA0BmB,UAAUqJ,eAAiB,SAAS3B,OACtD/B,KAAOnH,EAAEkJ,EAAEC,QAAQrH,QAAQ,gBACX,IAAhBqF,KAAKjF,OAAc,KACfoI,WAAatK,EAAEkJ,EAAEC,QACjB1B,aAAe3G,KAAK8E,0BAA0B0E,WAAY,WACzC,OAAjB7C,eACAN,KAAOrG,KAAK4I,QAAQY,WAAY7C,mBAGpCqD,YAAchK,KAAK2J,sBAAsB3J,KAAKsG,SAASD,OACvD4D,SAAW/K,WAEPkJ,EAAE8B,cACD9K,KAAK+K,WACL/K,KAAKgL,gBACLhL,KAAKiL,UACNJ,SAAWjK,KAAKsK,YAAYtK,KAAKsB,SAAS+E,MAAO2D,wBAGhD5K,KAAKmL,eACLnL,KAAKoL,QACNP,SAAWjK,KAAKyK,gBAAgBzK,KAAKsB,SAAS+E,MAAO2D,wBAGpD5K,KAAKsL,OACNvI,gBAAgBwI,sBAAuB,4BAIvCxI,gBAAgBwI,sBAAuB,MAI3CV,SAAS7I,OAAQ,CACjB6I,SAASzD,KAAK,WAAW,GACzByD,SAAS5J,SAAS,oBACd6G,WAAalH,KAAKmH,aAAa8C,aAC/B/C,WAAW9F,UACP6I,SAAShJ,SAAS,YAAa,KAC3B8E,UAAY/F,KAAKgG,iBAAiBhG,KAAKsB,SAAS2I,cACnCjK,KAAKoH,sBAAsB6C,UAAU,GACvC7I,OAAS2E,UAAW,KAC3BE,UAAYgE,SAAS/H,QACzB+D,UAAUJ,YAAY,gBACtBI,UAAUW,WAAW,YACrBM,WAAWG,MAAMpB,WACjB9D,gBAAgBC,uBAAuB6D,WACvCgE,SAAS/D,OAAOD,UAAUC,eAE1BgB,WAAW7G,SAAS,UACpB4J,SAAS/D,OAAOgB,WAAWhB,eAG/BgB,WAAW7G,SAAS,UACpB4J,SAAS/D,OAAOgB,WAAWhB,eAInCG,KAAKG,KAAK,WAAW,GAGzB4B,EAAEwC,sBACGtD,eAAe2C,SAAU5D,OAUlC9G,0BAA0BmB,UAAU4J,YAAc,SAASjJ,MAAO4D,UAC1D1D,OACAsJ,WAAa7K,KAAK8K,mBAAmBzJ,OAGrCE,OADgB,IAAhB0D,KAAK7D,OACI,EAEApB,KAAKwB,UAAUyD,MAAQ,UAGhC8F,KAAO/K,KAAKiH,kBAAkB5F,MAAOE,QAClB,IAAhBwJ,KAAK3J,QAAgBG,OAASsJ,YACjCtJ,SACAwJ,KAAO/K,KAAKiH,kBAAkB5F,MAAOE,eAGlCwJ,MAUXxL,0BAA0BmB,UAAU+J,gBAAkB,SAASpJ,MAAO4D,UAC9D1D,OAGAA,OADgB,IAAhB0D,KAAK7D,OACIpB,KAAK8K,mBAAmBzJ,OAExBrB,KAAKwB,UAAUyD,MAAQ,UAGhC+F,SAAWhL,KAAKiH,kBAAkB5F,MAAOE,QAClB,IAApByJ,SAAS5J,QAAgBG,OAAS,GACrCA,SACAyJ,SAAWhL,KAAKiH,kBAAkB5F,MAAOE,eAItCyJ,UASXzL,0BAA0BmB,UAAUmJ,UAAY,SAAS5E,KAAMoD,YACvD4C,WAAahG,KAAKiB,SAClBgF,UAAY7C,OAAOnC,SACnB5F,MAAQN,KAEZH,EAAEC,KAAKC,WAAW,+BAAiCO,MAAMd,aAKzDyF,KAAKkG,QACD,CACIC,KAAM7F,SAASN,KAAKzB,IAAI,SAAW0H,UAAUE,KAAOH,WAAWG,KAC/D9H,IAAKiC,SAASN,KAAKzB,IAAI,QAAU0H,UAAU5H,IAAM2H,WAAW3H,KAEhE,CACI+H,SAAU,OACVC,KAAM,WACFpM,EAAE,QAAQqM,QAAQ,gCAAiC,CAACtG,KAAMoD,OAAQ/H,QAClET,EAAEC,KAAK+E,YAAY,+BAAiCvE,MAAMd,iBAc1ED,0BAA0BmB,UAAU0I,cAAgB,SAASH,MAAOC,MAAO7C,UACnEmF,SAAWnF,KAAKH,gBAChBG,KAAKpF,SAAS,YACPgI,OAASuC,SAASJ,MAAQnC,MAAQuC,SAASJ,KAAO/E,KAAKoF,cACvDvC,OAASsC,SAASlI,KAAO4F,MAAQsC,SAASlI,IAAM+C,KAAKqF,cAEzDzC,OAASuC,SAASJ,MAAQnC,MAAQuC,SAASJ,KAAO/E,KAAKzD,SACvDsG,OAASsC,SAASlI,KAAO4F,MAAQsC,SAASlI,IAAM+C,KAAK3D,UAShEnD,0BAA0BmB,UAAUgI,cAAgB,SAAStD,MAAO7D,aAC3DnB,UAAUe,KAAK,yBAA2BiE,OAAO2B,IAAIxF,SAQ9DhC,0BAA0BmB,UAAUN,QAAU,kBACnClB,EAAE0E,SAAS+H,eAAe3L,KAAKR,eAO1CD,0BAA0BmB,UAAUkL,QAAU,kBACnC5L,KAAKI,UAAUe,KAAK,uBAU/B5B,0BAA0BmB,UAAUoJ,YAAc,SAASzI,MAAOE,eACzDvB,KAAKI,UAAUe,KAAK,kCAAoCE,MAAQ,UAAYE,QAAQsK,GAAG,YAMrF7L,KAAKI,UAAUe,KAAK,kCAAoCE,MAAQ,UAAYE,QALxEvB,KAAKI,UAAUe,KAAK,iBAAmBE,MAAnB,6BAEXE,OACZ,SAAWF,QAYvB9B,0BAA0BmB,UAAUuG,kBAAoB,SAAS5F,MAAOE,eAC7DvB,KAAKI,UAAUe,KAAK,0BAA4BE,MAAQ,UAAYE,OAAS,aAAauK,MAAM,EAAG,IAS9GvM,0BAA0BmB,UAAUiJ,sBAAwB,SAASvE,cAC1DpF,KAAKI,UAAUe,KAAK,4BAA8BiE,QAS7D7F,0BAA0BmB,UAAUsF,iBAAmB,SAAS3E,cACrDrB,KAAKI,UAAUe,KAAK,kBAAoBE,OAAOD,QAS1D7B,0BAA0BmB,UAAUoK,mBAAqB,SAASzJ,cACvDrB,KAAKI,UAAUe,KAAK,iBAAmBE,MAAQ,cAAcD,QAUxE7B,0BAA0BmB,UAAUoE,0BAA4B,SAASlD,KAAMmK,YACvEC,QAAUpK,KAAKG,KAAK,YACR,KAAZiK,gBACIC,WAAaD,QAAQE,MAAM,KACtBxG,MAAQ,EAAGA,MAAQuG,WAAW7K,OAAQsE,QAAS,IACxC,IAAIyG,OAAO,IAAMJ,OAAS,aAC5BK,KAAKH,WAAWvG,QAAS,KAE3B2G,MADQ,IAAIF,OAAO,aACLG,KAAKL,WAAWvG,eAC3B6G,OAAOF,MAAM,YAIzB,MASX9M,0BAA0BmB,UAAUc,UAAY,SAASyD,aAC9CjF,KAAK8E,0BAA0BG,KAAM,WAUhD1F,0BAA0BmB,UAAUY,SAAW,SAASM,aAC7C5B,KAAK8E,0BAA0BlD,KAAM,UAShDrC,0BAA0BmB,UAAU4F,SAAW,SAAS1E,aAC7C5B,KAAK8E,0BAA0BlD,KAAM,UAShDrC,0BAA0BmB,UAAUyG,aAAe,SAASlC,aACjDjF,KAAKI,UAAUe,KAAK,iBACvBnB,KAAKsB,SAAS2D,MADS,oBAGXjF,KAAKwB,UAAUyD,MAC3B,SAAWjF,KAAKsB,SAAS2D,MACzB,qBAUR1F,0BAA0BmB,UAAU0G,sBAAwB,SAASnC,KAAMuH,eACnEA,OACOxM,KAAKI,UAAUe,KAAK,iBACvBnB,KAAKsB,SAAS2D,MADS,oBAGXjF,KAAKwB,UAAUyD,MAC3B,SAAWjF,KAAKsB,SAAS2D,MACzB,aAAab,IAAI,oBAElBpE,KAAKI,UAAUe,KAAK,mBACXnB,KAAKwB,UAAUyD,MAC3B,SAAWjF,KAAKsB,SAAS2D,MACzB,aAAab,IAAI,qBAUzB7E,0BAA0BmB,UAAUkI,QAAU,SAAS3D,KAAM0B,qBAClD3G,KAAKI,UAAUe,KAAK,kBAAoBnB,KAAKsB,SAAS2D,MAAQ,SAAW0B,eAMpFpH,0BAA0BmB,UAAU+L,aAAe,eAC3CnM,MAAQN,KACRmG,QAAUnG,KAAKmG,UACfnG,KAAKG,aACLgG,QAAU,QAGT/F,UAAUe,KAAK,qBAAqBO,MAAK,SAASC,EAAGyE,UACtDlH,EAAEkH,UACG5C,IAAI,OAAQ+B,SAASrG,EAAEkH,UAAUI,KAAK,YAAckG,WAAWvG,UAC/D3C,IAAI,MAAO+B,SAASrG,EAAEkH,UAAUI,KAAK,YAAckG,WAAWvG,UACnE7F,MAAMmG,mBAAmBL,SAAU,oBAGlChG,UAAUe,KAAK,0BAA0BiD,IAAI,iBAAiB1C,MAAK,SAASwG,IAAKjD,MAClF/F,EAAE+F,MACGzB,IAAI,OAAQkJ,WAAWxN,EAAE+F,MAAMuB,KAAK,YAAckG,WAAWvG,UAC7D3C,IAAI,MAAOkJ,WAAWxN,EAAE+F,MAAMuB,KAAK,YAAckG,WAAWvG,UACjE7F,MAAMmG,mBAAmBxB,KAAM,gBASvC1F,0BAA0BmB,UAAUyF,QAAU,eACtCwG,MAAQ3M,KAAK4L,UACbgB,kBAAoBD,MAAM9K,IAAI,GAAGgL,oBACdF,MAAM/J,QAEHgK,mBAS9BrN,0BAA0BmB,UAAU+F,mBAAqB,SAAStC,QAAS2I,UACnE3G,QAAUuG,WAAW1M,KAAKmG,WAC1BnG,KAAKG,aACLgG,QAAU,GAEdjH,EAAEiF,SAASX,IAAI,qBACU,SAAW2C,QAAU,qBACxB,SAAWA,QAAU,oBACtB,SAAWA,QAAU,mBACtB,SAAWA,QAAU,cACxB,SAAWA,QAAU,uBACd2G,QAS5BvN,0BAA0BmB,UAAU6H,gBAAkB,eAC9CwE,OAAS,cACR3M,UAAUe,KAAK,6CAA6CO,MAAK,SAASC,EAAGyE,cAI1E4G,YAHJ5G,SAAWlH,EAAEkH,WAGa5C,IAAI,WAAa+B,SAASa,SAAS5C,IAAI,YAAc,EAE3EwJ,WAAaD,SACbA,OAASC,eAIVD,QAUXxN,0BAA0BmB,UAAU2I,iBAAmB,SAASpE,KAAMoB,aAC3DrG,KAAKwB,UAAUyD,QAAUjF,KAAKwB,UAAU6E,OAASrG,KAAKsB,SAAS2D,QAAUjF,KAAKsB,SAAS+E,WAQ9FlE,gBAAkB,CAKlB8K,0BAA0B,EAM1BC,6BAA8B,GAK9B/M,YAAY,EAKZwK,sBAAsB,EAKtBwC,UAAW,GAUXC,KAAM,SAAS5N,YAAaC,SAAUC,WAClCyC,gBAAgBgL,UAAU3N,aACtB,IAAID,0BAA0BC,YAAaC,SAAUC,QACpDyC,gBAAgB8K,2BACjB9K,gBAAgBkL,qBAChBlL,gBAAgB8K,0BAA2B,IAE1C9K,gBAAgB+K,6BAA6B/H,eAAe3F,aAAc,CAC3E2C,gBAAgB+K,6BAA6B1N,cAAe,MAExD8N,kBAAoB1J,SAAS+H,eAAenM,aAC5C8N,kBAAkBvK,UAAUwK,SAAS,mBACpCD,kBAAkBvK,UAAUwK,SAAS,iCAEtCpL,gBAAgBC,uBAAuBlD,EAAEoO,mBAAmBnM,KAAK,gBAQ7EkM,mBAAoB,WAChBnO,EAAE,QACGsO,GAAG,UACA,6EACArL,gBAAgB4H,gBACnByD,GAAG,UACA,4FACArL,gBAAgB4H,gBACnByD,GAAG,gCAAiCrL,gBAAgBsL,iBACzDvO,EAAEwO,QAAQF,GAAG,UAAU,WACnBrL,gBAAgBwL,oBAAmB,MAEvCD,OAAO7J,iBAAiB,eAAe,WACnC1B,gBAAgBhC,YAAa,EAC7BgC,gBAAgBwL,mBAAmBxL,gBAAgBhC,eAEvDuN,OAAO7J,iBAAiB,cAAc,WAClC1B,gBAAgBhC,YAAa,EAC7BgC,gBAAgBwL,mBAAmBxL,gBAAgBhC,eAEvDuD,YAAW,WACPvB,gBAAgByL,2BACjB,MAQPxL,uBAAwB,SAAS+B,SAE7BA,QAAQ0J,OAAO,wBACf1J,QAAQqJ,GAAG,uBAAwBrL,gBAAgBgG,kBAOvDA,gBAAiB,SAASC,GACtBA,EAAEwC,qBACEkD,SAAW3L,gBAAgB4L,oBAAoB3F,GAC/C0F,UACAA,SAAS3F,gBAAgBC,IAQjC2B,eAAgB,SAAS3B,OACjBjG,gBAAgBwI,sBAGpBxI,gBAAgBwI,sBAAuB,MACnCmD,SAAW3L,gBAAgB4L,oBAAoB3F,GAC/C0F,UACAA,SAAS/D,eAAe3B,KAQhCuF,mBAAoB,SAASxN,gBACpB,IAAIX,eAAe2C,gBAAgBgL,UAChChL,gBAAgBgL,UAAUhI,eAAe3F,eACzC2C,gBAAgBgL,UAAU3N,aAAaW,WAAaA,WACpDgC,gBAAgBgL,UAAU3N,aAAaiN,iBAUnDmB,uBAAwB,gBACfD,mBAAmBxL,gBAAgBhC,YAIxCuD,YAAW,WACPvB,gBAAgByL,uBAAuBzL,gBAAgBhC,cACxD,MAWPsN,gBAAiB,SAASrF,EAAGnD,KAAMoD,OAAQ/H,OACvC2E,KAAKY,YAAY,gBAAgBrC,IAAI,UAAW,IAChDyB,KAAKzB,IAAI,MAAO6E,OAAOmD,WAAWlI,KAAKE,IAAI,OAAQ6E,OAAOmD,WAAWJ,MACrE/C,OAAOhB,MAAMpC,MACboD,OAAOxC,YAAY,eACkB,IAA1BZ,KAAKuB,KAAK,cAAyD,IAA1BvB,KAAKuB,KAAK,aAC1DvB,KAAKY,YAAY,UAAUxF,SAAS,YACpC4E,KAAK2B,WAAW,YAChB3B,KAAK+I,WAAW,YAChB/I,KAAKzB,IAAI,MAAO,IACXA,IAAI,OAAQ,IACZA,IAAI,YAAa,IAClByB,KAAKhE,SAAS,aAAeX,MAAM8G,sBAAsBnC,MAAM,GAAM7D,OAAS,GAC9Ed,MAAM8G,sBAAsBnC,MAAM,GAAMgJ,QAAQzL,WAGpDyC,KAAKuB,KAAK,UAAW6B,OAAO7B,KAAK,YAAYA,KAAK,UAAW6B,OAAO7B,KAAK,YACzElG,MAAMmG,mBAAmBxB,KAAM,kBAEC,IAAzBA,KAAKuB,KAAK,aAAuD,IAAzBvB,KAAKuB,KAAK,aACzDvB,KAAK2E,QACL3E,KAAK+I,WAAW,iBAEkB,IAA3B3F,OAAO7B,KAAK,aAAyD,IAA3B6B,OAAO7B,KAAK,YAC7D6B,OAAO2F,WAAW,WAElB7L,gBAAgBwI,uBAChBxI,gBAAgBwI,sBAAuB,GAEvCrK,MAAMqH,yBAENxF,gBAAgB+L,kBAEhB5N,MAAMX,eAAiBW,MAAMiH,8BASrCwG,oBAAqB,SAAS3F,OACtB5I,YAAcN,EAAEkJ,EAAE+F,eAAenN,QAAQ,sBAAsBe,KAAK,aACjEI,gBAAgBgL,UAAU3N,cAMrC0O,gBAAiB,iBACPE,aAAexK,SAAS+H,eAAe,gBAC7CtM,kBAAkBgP,gBAAgBD,sBAOnC,CACHhB,KAAMjL,gBAAgBiL"}