/*
 * JavaScript to allow dragging options to slots (using mouse down or touch) or tab through slots using keyboard.
 *
 * @module     qtype_ddimageortext/question
 * @copyright  2018 The Open University
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("qtype_ddimageortext/question",["jquery","core/dragdrop","core/key_codes","core_form/changechecker","core_filters/events"],(function($,dragDrop,keys,FormChangeChecker,filterEvent){function DragDropOntoImageQuestion(containerId,readOnly,places){this.containerId=containerId,this.questionAnswer={},this.questionDragDropWidthHeight=[],M.util.js_pending("qtype_ddimageortext-init-"+this.containerId),this.places=places,this.allImagesLoaded=!1,this.imageLoadingTimeoutId=null,this.isPrinting=!1,readOnly&&this.getRoot().addClass("qtype_ddimageortext-readonly");var thisQ=this;this.getNotYetLoadedImages().one("load",(function(){thisQ.waitForAllImagesToBeLoaded()})),this.waitForAllImagesToBeLoaded()}DragDropOntoImageQuestion.prototype.changeAllDragsAndDropsToFilteredContent=function(filteredElement){let currentFilteredItem=$(filteredElement);const parentIsDD=currentFilteredItem.parent().closest("div").hasClass("placed")||currentFilteredItem.parent().hasClass("draghome"),isDD=currentFilteredItem.hasClass("placed")||currentFilteredItem.hasClass("draghome");if(!parentIsDD&&!isDD)return;if(parentIsDD&&(currentFilteredItem=currentFilteredItem.parent().closest("div")),this.getRoot().find(currentFilteredItem).length<=0)return;const group=this.getGroup(currentFilteredItem),choice=this.getChoice(currentFilteredItem);let listOfModifiedDragDrop=[];this.getRoot().find(".group"+group+".choice"+choice).each((function(i,node){if($(node).get(0)===currentFilteredItem.get(0))return;const originalClass=$(node).attr("class"),originalStyle=$(node).attr("style"),filteredDragDropClone=currentFilteredItem.clone();questionManager.addEventHandlersToDrag(filteredDragDropClone),filteredDragDropClone.attr("class",originalClass),filteredDragDropClone.attr("style",originalStyle),$(node).before(filteredDragDropClone),listOfModifiedDragDrop.push(node)})),listOfModifiedDragDrop.forEach((function(node){$(node).remove()}));const currentHeight=currentFilteredItem.height(),currentWidth=currentFilteredItem.width();if(currentFilteredItem.height("auto"),currentFilteredItem.width("auto"),filteredElement.offsetWidth&&filteredElement.offsetHeight||filteredElement.classList.add("d-block"),this.questionDragDropWidthHeight[group].maxWidth<Math.ceil(filteredElement.offsetWidth)||this.questionDragDropWidthHeight[group].maxHeight<Math.ceil(0+filteredElement.offsetHeight))filteredElement.classList.remove("d-block"),this.resizeAllDragsAndDropsInGroup(group);else{const top=Math.floor((this.questionDragDropWidthHeight[group].maxHeight-filteredElement.offsetHeight)/2);currentFilteredItem.width(currentWidth).height(currentHeight).css({"padding-top":top+"px"})}filteredElement.classList.remove("d-block")},DragDropOntoImageQuestion.prototype.waitForAllImagesToBeLoaded=function(){var thisQ=this;this.allImagesLoaded||(null!==this.imageLoadingTimeoutId&&clearTimeout(this.imageLoadingTimeoutId),this.getNotYetLoadedImages().length>0?this.imageLoadingTimeoutId=setTimeout((function(){thisQ.waitForAllImagesToBeLoaded()}),100):(this.allImagesLoaded=!0,thisQ.setupQuestion(),document.addEventListener(filterEvent.eventTypes.filterContentRenderingComplete,(elements=>{elements.detail.nodes.forEach((element=>{thisQ.changeAllDragsAndDropsToFilteredContent(element)}))}))))},DragDropOntoImageQuestion.prototype.getNotYetLoadedImages=function(){var thisQ=this;return this.getRoot().find(".ddarea img").not((function(i,imgNode){return thisQ.imageIsLoaded(imgNode)}))},DragDropOntoImageQuestion.prototype.imageIsLoaded=function(imgElement){return imgElement.complete&&0!==imgElement.naturalHeight},DragDropOntoImageQuestion.prototype.setupQuestion=function(){this.resizeAllDragsAndDrops(),this.cloneDrags(),this.positionDragsAndDrops(),M.util.js_complete("qtype_ddimageortext-init-"+this.containerId)},DragDropOntoImageQuestion.prototype.resizeAllDragsAndDrops=function(){var thisQ=this;this.getRoot().find(".draghomes > div").each((function(i,node){thisQ.resizeAllDragsAndDropsInGroup(thisQ.getClassnameNumericSuffix($(node),"dragitemgroup"))}))},DragDropOntoImageQuestion.prototype.resizeAllDragsAndDropsInGroup=function(group){var root=this.getRoot(),dragHomes=root.find(".draghome.group"+group),maxWidth=0,maxHeight=0;for(var i in dragHomes.each((function(i,drag){maxWidth=Math.max(maxWidth,Math.ceil(drag.offsetWidth)),maxHeight=Math.max(maxHeight,Math.ceil(drag.offsetHeight))})),maxWidth+=10,maxHeight+=10,this.questionDragDropWidthHeight[group]={maxWidth:maxWidth,maxHeight:maxHeight},dragHomes.each((function(i,drag){const top=Math.floor((maxHeight-drag.offsetHeight)/2);$(drag).width(maxWidth).height(maxHeight).css({"padding-top":top+"px"})})),this.places)if(this.places.hasOwnProperty(i)){var place=this.places[i],label=place.text;parseInt(place.group)===group&&(""===label&&(label=M.util.get_string("blank","qtype_ddimageortext")),0===root.find(".dropzones .dropzone.group"+place.group+".place"+i).length&&root.find(".dropzones").append('<div class="dropzone active group'+place.group+" place"+i+'" tabindex="0"><span class="accesshide">'+label+"</span>&nbsp;</div>"),root.find(".dropzone.place"+i).width(maxWidth-2).height(maxHeight-2))}},DragDropOntoImageQuestion.prototype.cloneDrags=function(){var thisQ=this;thisQ.getRoot().find(".draghome").each((function(index,dragHome){var drag=$(dragHome),placeHolder=drag.clone();placeHolder.removeClass(),placeHolder.addClass("draghome choice"+thisQ.getChoice(drag)+" group"+thisQ.getGroup(drag)+" dragplaceholder"),drag.before(placeHolder)}))},DragDropOntoImageQuestion.prototype.cloneDragsForOneChoice=function(dragHome){if(dragHome.hasClass("infinite"))for(var noOfDrags=this.noOfDropsInGroup(this.getGroup(dragHome)),i=0;i<noOfDrags;i++)this.cloneDrag(dragHome);else this.cloneDrag(dragHome)},DragDropOntoImageQuestion.prototype.cloneDrag=function(dragHome){var drag=dragHome.clone();drag.removeClass("draghome").addClass("drag unplaced moodle-has-zindex").offset(dragHome.offset()),this.getRoot().find(".dragitems").append(drag)},DragDropOntoImageQuestion.prototype.positionDragsAndDrops=function(){var thisQ=this,root=this.getRoot(),bgRatio=this.bgRatio();root.find(".ddarea .dropzone").each((function(i,dropNode){var drop=$(dropNode),place=thisQ.places[thisQ.getPlace(drop)];drop.css("left",parseInt(place.xy[0])*bgRatio).css("top",parseInt(place.xy[1])*bgRatio),drop.data("originX",parseInt(place.xy[0])).data("originY",parseInt(place.xy[1])),thisQ.handleElementScale(drop,"left top")})),root.find(".draghome").not(".dragplaceholder").each((function(i,dragNode){var drag=$(dragNode),currentPlace=thisQ.getClassnameNumericSuffix(drag,"inplace");drag.addClass("unplaced").removeClass("placed"),drag.removeAttr("tabindex"),null!==currentPlace&&drag.removeClass("inplace"+currentPlace)})),root.find("input.placeinput").each((function(i,inputNode){var input=$(inputNode),choice=input.val();if(!(0===choice.length||choice.length>0&&"0"===choice)){var place=thisQ.getPlace(input),unplacedDrag=thisQ.getUnplacedChoice(thisQ.getGroup(input),choice),hiddenDrag=thisQ.getDragClone(unplacedDrag);if(hiddenDrag.length)if(unplacedDrag.hasClass("infinite")){var noOfDrags=thisQ.noOfDropsInGroup(thisQ.getGroup(unplacedDrag));if(thisQ.getInfiniteDragClones(unplacedDrag,!1).length<noOfDrags){var cloneDrag=unplacedDrag.clone();cloneDrag.removeClass("beingdragged"),cloneDrag.removeAttr("tabindex"),hiddenDrag.after(cloneDrag),questionManager.addEventHandlersToDrag(cloneDrag)}else hiddenDrag.addClass("active")}else hiddenDrag.addClass("active");var drop=root.find(".dropzone.place"+place);thisQ.sendDragToDrop(unplacedDrag,drop)}})),thisQ.questionAnswer=thisQ.getQuestionAnsweredValues()},DragDropOntoImageQuestion.prototype.getQuestionAnsweredValues=function(){let result={};return this.getRoot().find("input.placeinput").each(((i,inputNode)=>{result[inputNode.id]=inputNode.value})),result},DragDropOntoImageQuestion.prototype.isQuestionInteracted=function(){const oldAnswer=this.questionAnswer,newAnswer=this.getQuestionAnsweredValues();let isInteracted=!1;return JSON.stringify(newAnswer)!==JSON.stringify(oldAnswer)?(isInteracted=!0,isInteracted):(Object.keys(newAnswer).forEach((key=>{newAnswer[key]!==oldAnswer[key]&&(isInteracted=!0)})),isInteracted)},DragDropOntoImageQuestion.prototype.handleDragStart=function(e){var thisQ=this,drag=$(e.target).closest(".draghome"),newIndex=this.calculateZIndex()+2;if(dragDrop.prepare(e).start&&!drag.hasClass("beingdragged")){drag.addClass("beingdragged").css("transform","").css("z-index",newIndex);var currentPlace=this.getClassnameNumericSuffix(drag,"inplace");if(null!==currentPlace){this.setInputValue(currentPlace,0),drag.removeClass("inplace"+currentPlace);var hiddenDrop=thisQ.getDrop(drag,currentPlace);hiddenDrop.length&&(hiddenDrop.addClass("active"),drag.offset(hiddenDrop.offset()))}else{var hiddenDrag=thisQ.getDragClone(drag);if(hiddenDrag.length)if(drag.hasClass("infinite")){var noOfDrags=this.noOfDropsInGroup(thisQ.getGroup(drag));if(this.getInfiniteDragClones(drag,!1).length<noOfDrags){var cloneDrag=drag.clone();cloneDrag.removeClass("beingdragged"),cloneDrag.removeAttr("tabindex"),hiddenDrag.after(cloneDrag),questionManager.addEventHandlersToDrag(cloneDrag),drag.offset(cloneDrag.offset())}else hiddenDrag.addClass("active"),drag.offset(hiddenDrag.offset())}else hiddenDrag.addClass("active"),drag.offset(hiddenDrag.offset())}dragDrop.start(e,drag,(function(x,y,drag){thisQ.dragMove(x,y,drag)}),(function(x,y,drag){thisQ.dragEnd(x,y,drag)}))}},DragDropOntoImageQuestion.prototype.dragMove=function(pageX,pageY,drag){var thisQ=this,highlighted=!1;this.getRoot().find(".dropzone.group"+this.getGroup(drag)).each((function(i,dropNode){var drop=$(dropNode);thisQ.isPointInDrop(pageX,pageY,drop)&&!highlighted?(highlighted=!0,drop.addClass("valid-drag-over-drop")):drop.removeClass("valid-drag-over-drop")})),this.getRoot().find(".draghome.placed.group"+this.getGroup(drag)).not(".beingdragged").each((function(i,dropNode){var drop=$(dropNode);!thisQ.isPointInDrop(pageX,pageY,drop)||highlighted||thisQ.isDragSameAsDrop(drag,drop)?drop.removeClass("valid-drag-over-drop"):(highlighted=!0,drop.addClass("valid-drag-over-drop"))}))},DragDropOntoImageQuestion.prototype.dragEnd=function(pageX,pageY,drag){var thisQ=this,root=this.getRoot(),placed=!1;root.find(".dropzone.group"+this.getGroup(drag)).each((function(i,dropNode){var drop=$(dropNode);return!thisQ.isPointInDrop(pageX,pageY,drop)||(drop.removeClass("valid-drag-over-drop"),thisQ.sendDragToDrop(drag,drop),placed=!0,!1)})),placed||root.find(".draghome.placed.group"+this.getGroup(drag)).not(".beingdragged").each((function(i,placedNode){var placedDrag=$(placedNode);if(!thisQ.isPointInDrop(pageX,pageY,placedDrag)||thisQ.isDragSameAsDrop(drag,placedDrag))return!0;placedDrag.removeClass("valid-drag-over-drop");var currentPlace=thisQ.getClassnameNumericSuffix(placedDrag,"inplace"),drop=thisQ.getDrop(drag,currentPlace);return thisQ.sendDragToDrop(drag,drop),placed=!0,!1})),placed||this.sendDragHome(drag)},DragDropOntoImageQuestion.prototype.sendDragToDrop=function(drag,drop){var oldDrag=this.getCurrentDragInPlace(this.getPlace(drop));if(0!==oldDrag.length){oldDrag.addClass("beingdragged"),oldDrag.offset(oldDrag.offset());var currentPlace=this.getClassnameNumericSuffix(oldDrag,"inplace");this.getDrop(oldDrag,currentPlace).addClass("active"),this.sendDragHome(oldDrag)}0===drag.length?(this.setInputValue(this.getPlace(drop),0),drop.data("isfocus")&&drop.focus()):(this.setInputValue(this.getPlace(drop),this.getChoice(drag)),drag.removeClass("unplaced").addClass("placed inplace"+this.getPlace(drop)),drag.attr("tabindex",0),this.animateTo(drag,drop))},DragDropOntoImageQuestion.prototype.sendDragHome=function(drag){var currentPlace=this.getClassnameNumericSuffix(drag,"inplace");null!==currentPlace&&drag.removeClass("inplace"+currentPlace),drag.data("unplaced",!0),this.animateTo(drag,this.getDragHome(this.getGroup(drag),this.getChoice(drag)))},DragDropOntoImageQuestion.prototype.handleKeyPress=function(e){var drop=$(e.target).closest(".dropzone");if(0===drop.length){var placedDrag=$(e.target),currentPlace=this.getClassnameNumericSuffix(placedDrag,"inplace");null!==currentPlace&&(drop=this.getDrop(placedDrag,currentPlace))}var currentDrag=this.getCurrentDragInPlace(this.getPlace(drop)),nextDrag=$();switch(e.keyCode){case keys.space:case keys.arrowRight:case keys.arrowDown:nextDrag=this.getNextDrag(this.getGroup(drop),currentDrag);break;case keys.arrowLeft:case keys.arrowUp:nextDrag=this.getPreviousDrag(this.getGroup(drop),currentDrag);break;case keys.escape:questionManager.isKeyboardNavigation=!1;break;default:return void(questionManager.isKeyboardNavigation=!1)}if(nextDrag.length){nextDrag.data("isfocus",!0),nextDrag.addClass("beingdragged");var hiddenDrag=this.getDragClone(nextDrag);if(hiddenDrag.length)if(nextDrag.hasClass("infinite")){var noOfDrags=this.noOfDropsInGroup(this.getGroup(nextDrag));if(this.getInfiniteDragClones(nextDrag,!1).length<noOfDrags){var cloneDrag=nextDrag.clone();cloneDrag.removeClass("beingdragged"),cloneDrag.removeAttr("tabindex"),hiddenDrag.after(cloneDrag),questionManager.addEventHandlersToDrag(cloneDrag),nextDrag.offset(cloneDrag.offset())}else hiddenDrag.addClass("active"),nextDrag.offset(hiddenDrag.offset())}else hiddenDrag.addClass("active"),nextDrag.offset(hiddenDrag.offset())}else drop.data("isfocus",!0);e.preventDefault(),this.sendDragToDrop(nextDrag,drop)},DragDropOntoImageQuestion.prototype.getNextDrag=function(group,drag){var choice,numChoices=this.noOfChoicesInGroup(group);choice=0===drag.length?1:this.getChoice(drag)+1;for(var next=this.getUnplacedChoice(group,choice);0===next.length&&choice<numChoices;)choice++,next=this.getUnplacedChoice(group,choice);return next},DragDropOntoImageQuestion.prototype.getPreviousDrag=function(group,drag){var choice;choice=0===drag.length?this.noOfChoicesInGroup(group):this.getChoice(drag)-1;for(var previous=this.getUnplacedChoice(group,choice);0===previous.length&&choice>1;)choice--,previous=this.getUnplacedChoice(group,choice);return previous},DragDropOntoImageQuestion.prototype.animateTo=function(drag,target){var currentPos=drag.offset(),targetPos=target.offset(),thisQ=this;M.util.js_pending("qtype_ddimageortext-animate-"+thisQ.containerId),drag.animate({left:parseInt(drag.css("left"))+targetPos.left-currentPos.left,top:parseInt(drag.css("top"))+targetPos.top-currentPos.top},{duration:"fast",done:function(){$("body").trigger("qtype_ddimageortext-dragmoved",[drag,target,thisQ]),M.util.js_complete("qtype_ddimageortext-animate-"+thisQ.containerId)}})},DragDropOntoImageQuestion.prototype.isPointInDrop=function(pageX,pageY,drop){var position=drop.offset();return drop.hasClass("draghome")?pageX>=position.left&&pageX<position.left+drop.outerWidth()&&pageY>=position.top&&pageY<position.top+drop.outerHeight():pageX>=position.left&&pageX<position.left+drop.width()&&pageY>=position.top&&pageY<position.top+drop.height()},DragDropOntoImageQuestion.prototype.setInputValue=function(place,choice){this.getRoot().find("input.placeinput.place"+place).val(choice)},DragDropOntoImageQuestion.prototype.getRoot=function(){return $(document.getElementById(this.containerId))},DragDropOntoImageQuestion.prototype.bgImage=function(){return this.getRoot().find("img.dropbackground")},DragDropOntoImageQuestion.prototype.getDragHome=function(group,choice){return this.getRoot().find(".draghome.dragplaceholder.group"+group+".choice"+choice).is(":visible")?this.getRoot().find(".draghome.dragplaceholder.group"+group+".choice"+choice):this.getRoot().find(".dragitemgroup"+group+" .draghome.infinite.choice"+choice+".group"+group)},DragDropOntoImageQuestion.prototype.getUnplacedChoice=function(group,choice){return this.getRoot().find(".ddarea .draghome.group"+group+".choice"+choice+".unplaced").slice(0,1)},DragDropOntoImageQuestion.prototype.getCurrentDragInPlace=function(place){return this.getRoot().find(".ddarea .draghome.inplace"+place)},DragDropOntoImageQuestion.prototype.noOfDropsInGroup=function(group){return this.getRoot().find(".dropzone.group"+group).length},DragDropOntoImageQuestion.prototype.noOfChoicesInGroup=function(group){return this.getRoot().find(".dragitemgroup"+group+" .draghome").length},DragDropOntoImageQuestion.prototype.getClassnameNumericSuffix=function(node,prefix){var classes=node.attr("class");if(""!==classes)for(var classesArr=classes.split(" "),index=0;index<classesArr.length;index++){if(new RegExp("^"+prefix+"([0-9])+$").test(classesArr[index])){var match=new RegExp("([0-9])+$").exec(classesArr[index]);return Number(match[0])}}return null},DragDropOntoImageQuestion.prototype.getChoice=function(drag){return this.getClassnameNumericSuffix(drag,"choice")},DragDropOntoImageQuestion.prototype.getGroup=function(node){return this.getClassnameNumericSuffix(node,"group")},DragDropOntoImageQuestion.prototype.getPlace=function(node){return this.getClassnameNumericSuffix(node,"place")},DragDropOntoImageQuestion.prototype.getDragClone=function(drag){return this.getRoot().find(".dragitemgroup"+this.getGroup(drag)+" .draghome.choice"+this.getChoice(drag)+".group"+this.getGroup(drag)+".dragplaceholder")},DragDropOntoImageQuestion.prototype.getInfiniteDragClones=function(drag,inHome){return inHome?this.getRoot().find(".dragitemgroup"+this.getGroup(drag)+" .draghome.choice"+this.getChoice(drag)+".group"+this.getGroup(drag)+".infinite").not(".dragplaceholder"):this.getRoot().find(".draghome.choice"+this.getChoice(drag)+".group"+this.getGroup(drag)+".infinite").not(".dragplaceholder")},DragDropOntoImageQuestion.prototype.getDrop=function(drag,currentPlace){return this.getRoot().find(".dropzone.group"+this.getGroup(drag)+".place"+currentPlace)},DragDropOntoImageQuestion.prototype.handleResize=function(){var thisQ=this,bgRatio=this.bgRatio();this.isPrinting&&(bgRatio=1),this.getRoot().find(".ddarea .dropzone").each((function(i,dropNode){$(dropNode).css("left",parseInt($(dropNode).data("originX"))*parseFloat(bgRatio)).css("top",parseInt($(dropNode).data("originY"))*parseFloat(bgRatio)),thisQ.handleElementScale(dropNode,"left top")})),this.getRoot().find("div.droparea .draghome").not(".beingdragged").each((function(key,drag){$(drag).css("left",parseFloat($(drag).data("originX"))*parseFloat(bgRatio)).css("top",parseFloat($(drag).data("originY"))*parseFloat(bgRatio)),thisQ.handleElementScale(drag,"left top")}))},DragDropOntoImageQuestion.prototype.bgRatio=function(){var bgImg=this.bgImage(),bgImgNaturalWidth=bgImg.get(0).naturalWidth;return bgImg.width()/bgImgNaturalWidth},DragDropOntoImageQuestion.prototype.handleElementScale=function(element,type){var bgRatio=parseFloat(this.bgRatio());this.isPrinting&&(bgRatio=1),$(element).css({"-webkit-transform":"scale("+bgRatio+")","-moz-transform":"scale("+bgRatio+")","-ms-transform":"scale("+bgRatio+")","-o-transform":"scale("+bgRatio+")",transform:"scale("+bgRatio+")","transform-origin":type})},DragDropOntoImageQuestion.prototype.calculateZIndex=function(){var zIndex=0;return this.getRoot().find(".ddarea .dropzone, div.droparea .draghome").each((function(i,dropNode){var itemZIndex=(dropNode=$(dropNode)).css("z-index")?parseInt(dropNode.css("z-index")):0;itemZIndex>zIndex&&(zIndex=itemZIndex)})),zIndex},DragDropOntoImageQuestion.prototype.isDragSameAsDrop=function(drag,drop){return this.getChoice(drag)===this.getChoice(drop)&&this.getGroup(drag)===this.getGroup(drop)};var questionManager={eventHandlersInitialised:!1,dragEventHandlersInitialised:{},isPrinting:!1,isKeyboardNavigation:!1,questions:{},init:function(containerId,readOnly,places){if(questionManager.questions[containerId]=new DragDropOntoImageQuestion(containerId,readOnly,places),questionManager.eventHandlersInitialised||(questionManager.setupEventHandlers(),questionManager.eventHandlersInitialised=!0),!questionManager.dragEventHandlersInitialised.hasOwnProperty(containerId)){questionManager.dragEventHandlersInitialised[containerId]=!0;var questionContainer=document.getElementById(containerId);questionContainer.classList.contains("ddimageortext")&&!questionContainer.classList.contains("qtype_ddimageortext-readonly")&&questionManager.addEventHandlersToDrag($(questionContainer).find(".draghome"))}},setupEventHandlers:function(){$("body").on("keydown",".que.ddimageortext:not(.qtype_ddimageortext-readonly) .dropzones .dropzone",questionManager.handleKeyPress).on("keydown",".que.ddimageortext:not(.qtype_ddimageortext-readonly) .draghome.placed:not(.beingdragged)",questionManager.handleKeyPress).on("qtype_ddimageortext-dragmoved",questionManager.handleDragMoved),$(window).on("resize",(function(){questionManager.handleWindowResize(!1)})),window.addEventListener("beforeprint",(function(){questionManager.isPrinting=!0,questionManager.handleWindowResize(questionManager.isPrinting)})),window.addEventListener("afterprint",(function(){questionManager.isPrinting=!1,questionManager.handleWindowResize(questionManager.isPrinting)})),setTimeout((function(){questionManager.fixLayoutIfThingsMoved()}),100)},addEventHandlersToDrag:function(element){element.unbind("mousedown touchstart"),element.on("mousedown touchstart",questionManager.handleDragStart)},handleDragStart:function(e){e.preventDefault();var question=questionManager.getQuestionForEvent(e);question&&question.handleDragStart(e)},handleKeyPress:function(e){if(!questionManager.isKeyboardNavigation){questionManager.isKeyboardNavigation=!0;var question=questionManager.getQuestionForEvent(e);question&&question.handleKeyPress(e)}},handleWindowResize:function(isPrinting){for(var containerId in questionManager.questions)questionManager.questions.hasOwnProperty(containerId)&&(questionManager.questions[containerId].isPrinting=isPrinting,questionManager.questions[containerId].handleResize())},fixLayoutIfThingsMoved:function(){this.handleWindowResize(questionManager.isPrinting),setTimeout((function(){questionManager.fixLayoutIfThingsMoved(questionManager.isPrinting)}),100)},handleDragMoved:function(e,drag,target,thisQ){drag.removeClass("beingdragged").css("z-index",""),drag.css("top",target.position().top).css("left",target.position().left),target.after(drag),target.removeClass("active"),void 0!==drag.data("unplaced")&&!0===drag.data("unplaced")?(drag.removeClass("placed").addClass("unplaced"),drag.removeAttr("tabindex"),drag.removeData("unplaced"),drag.css("top","").css("left","").css("transform",""),drag.hasClass("infinite")&&thisQ.getInfiniteDragClones(drag,!0).length>1&&thisQ.getInfiniteDragClones(drag,!0).first().remove()):(drag.data("originX",target.data("originX")).data("originY",target.data("originY")),thisQ.handleElementScale(drag,"left top")),void 0!==drag.data("isfocus")&&!0===drag.data("isfocus")&&(drag.focus(),drag.removeData("isfocus")),void 0!==target.data("isfocus")&&!0===target.data("isfocus")&&target.removeData("isfocus"),questionManager.isKeyboardNavigation&&(questionManager.isKeyboardNavigation=!1),thisQ.isQuestionInteracted()&&(questionManager.handleFormDirty(),thisQ.questionAnswer=thisQ.getQuestionAnsweredValues())},getQuestionForEvent:function(e){var containerId=$(e.currentTarget).closest(".que.ddimageortext").attr("id");return questionManager.questions[containerId]},handleFormDirty:function(){const responseForm=document.getElementById("responseform");FormChangeChecker.markFormAsDirty(responseForm)}};return{init:questionManager.init}}));

//# sourceMappingURL=question.min.js.map