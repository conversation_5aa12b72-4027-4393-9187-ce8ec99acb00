/*
 * JavaScript to allow dragging options to slots (using mouse down or touch) or tab through slots using keyboard.
 *
 * @module     qtype_ddimageortext/form
 * @copyright  2018 The Open University
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("qtype_ddimageortext/form",["jquery","core/dragdrop"],(function($,dragDrop){var dragDropToImageForm={maxBgImageSize:null,maxDragImageSize:null,fp:null,init:function(){dragDropToImageForm.fp=dragDropToImageForm.filePickers(),dragDropToImageForm.updateVisibilityOfFilePickers(),dragDropToImageForm.setOptionsForDragItemSelectors(),dragDropToImageForm.setupEventHandlers(),dragDropToImageForm.waitForFilePickerToInitialise()},setupPreviewArea:function(){$("#id_previewareaheader").append('<div class="ddarea que ddimageortext">  <div id="id_droparea" class="droparea">    <img class="dropbackground" />    <div class="dropzones"></div>  </div>  <div class="dragitems"></div></div>')},waitForFilePickerToInitialise:function(){null!==dragDropToImageForm.fp.file("bgimage").href?(M.util.js_pending("dragDropToImageForm"),$('form.mform[data-qtype="ddimageortext"]').on("change",".filepickerhidden",(function(){M.util.js_pending("dragDropToImageForm"),dragDropToImageForm.loadPreviewImage()})),$("#id_droparea").length||dragDropToImageForm.setupPreviewArea(),dragDropToImageForm.loadPreviewImage()):setTimeout(dragDropToImageForm.waitForFilePickerToInitialise,1e3)},loadPreviewImage:function(){$("fieldset#id_previewareaheader .dropbackground").one("load",dragDropToImageForm.afterPreviewImageLoaded).attr("src",dragDropToImageForm.fp.file("bgimage").href)},afterPreviewImageLoaded:function(){dragDropToImageForm.createDropZones(),M.util.js_complete("dragDropToImageForm")},createDropZones:function(){var dropZoneHolder=$(".dropzones");if(dropZoneHolder.empty(),null!==dragDropToImageForm.fp.file("bgimage").href){for(var numDrops=dragDropToImageForm.form.getFormValue("nodropzone",[]),dropzonevisibilitystyle="1"===dragDropToImageForm.form.getFormValue("dropzonevisibility",[])?"background: transparent;":"",dropNo=0;dropNo<numDrops;dropNo++){var dragNo=dragDropToImageForm.form.getFormValue("drops",[dropNo,"choice"]);if("0"!==dragNo){dragNo-=1;var group=dragDropToImageForm.form.getFormValue("drags",[dragNo,"draggroup"]),label=dragDropToImageForm.form.getFormValue("draglabel",[dragNo]);if("image"===dragDropToImageForm.form.getFormValue("drags",[dragNo,"dragitemtype"])){var imgUrl=dragDropToImageForm.fp.file("dragitem["+dragNo+"]").href;if(null===imgUrl)continue;dropZoneHolder.append('<img class="droppreview group'+group+" drop"+dropNo+'" src="'+imgUrl+'" alt="'+label+'" data-drop-no="'+dropNo+'" style="'+dropzonevisibilitystyle+'" >')}else""!==label&&dropZoneHolder.append('<div class="droppreview group'+group+" drop"+dropNo+'"  data-drop-no="'+dropNo+'">'+label+"</div>")}}dragDropToImageForm.waitForAllDropImagesToBeLoaded()}},waitForAllDropImagesToBeLoaded:function(){$(".dropzones img").not((function(i,imgNode){return dragDropToImageForm.imageIsLoaded(imgNode)})).length>0?setTimeout((function(){dragDropToImageForm.waitForAllDropImagesToBeLoaded()}),100):dragDropToImageForm.updateDropZones()},imageIsLoaded:function(imgElement){return imgElement.complete&&0!==imgElement.naturalHeight},updateDropZones:function(){if(null!==dragDropToImageForm.fp.file("bgimage").href){for(var dropBackgroundPosition=$("fieldset#id_previewareaheader .dropbackground").offset(),numDrops=dragDropToImageForm.form.getFormValue("nodropzone",[]),dropNo=0;dropNo<numDrops;dropNo++){var drop=$(".dropzones .drop"+dropNo);if(0!==drop.length){var dragNo=dragDropToImageForm.form.getFormValue("drops",[dropNo,"choice"])-1;drop.offset({left:dropBackgroundPosition.left+parseInt(dragDropToImageForm.form.getFormValue("drops",[dropNo,"xleft"])),top:dropBackgroundPosition.top+parseInt(dragDropToImageForm.form.getFormValue("drops",[dropNo,"ytop"]))});var label=dragDropToImageForm.form.getFormValue("draglabel",[dragNo]);drop.is("img")?drop.attr("alt",label):drop.html(label)}}$(".dropzones .droppreview").css("padding","0");for(var numGroups=$(".draggroup select").first().find("option").length,group=1;group<=numGroups;group++)dragDropToImageForm.resizeAllDragsAndDropsInGroup(group)}},resizeAllDragsAndDropsInGroup:function(group){var drops=$(".dropzones .droppreview.group"+group),maxWidth=0,maxHeight=0;drops.each((function(i,drop){maxWidth=Math.max(maxWidth,Math.ceil(drop.offsetWidth)),maxHeight=Math.max(maxHeight,Math.ceil(drop.offsetHeight))})),maxWidth+=10,maxHeight+=10,drops.each((function(i,drop){var left=Math.round((maxWidth-drop.offsetWidth)/2),top=Math.floor((maxHeight-drop.offsetHeight)/2);$(drop).css({"padding-left":left+"px","padding-right":maxWidth-drop.offsetWidth-left+"px","padding-top":top+"px","padding-bottom":maxHeight-drop.offsetHeight-top+"px"})}))},setupEventHandlers:function(){$("fieldset#id_draggableitemheader").on("change input","input, select",(function(e){var input=$(e.target).closest("select, input");input.hasClass("dragitemtype")&&dragDropToImageForm.updateVisibilityOfFilePickers(),dragDropToImageForm.setOptionsForDragItemSelectors(),input.is(".dragitemtype, .draggroup")?dragDropToImageForm.createDropZones():input.is(".draglabel")&&dragDropToImageForm.updateDropZones()})),$("fieldset#id_dropzoneheader").on("change input","input, select",(function(e){var input=$(e.target).closest("select, input");"id_dropzonevisibility"!==input.attr("id")&&(input.is("select")?dragDropToImageForm.createDropZones():dragDropToImageForm.updateDropZones())})),$("fieldset#id_previewareaheader").on("mousedown touchstart",".droppreview",(function(e){dragDropToImageForm.dragStart(e)})),$(window).on("resize",(function(){dragDropToImageForm.updateDropZones()})),$("#id_dropzonevisibility").on("change",(function(){let selectedvalue=$(this).val();"1"===selectedvalue?$(".droppreview").css("background","transparent"):"0"===selectedvalue&&$(".droppreview").css("background","")}))},updateVisibilityOfFilePickers:function(){for(var numDrags=dragDropToImageForm.form.getFormValue("noitems",[]),dragNo=0;dragNo<numDrags;dragNo++){var picker=$("input#id_dragitem_"+dragNo).closest(".fitem_ffilepicker");"image"===dragDropToImageForm.form.getFormValue("drags",[dragNo,"dragitemtype"])?picker.show():picker.hide()}},setOptionsForDragItemSelectors:function(){for(var dragItemOptions={0:""},numDrags=dragDropToImageForm.form.getFormValue("noitems",[]),numDrops=dragDropToImageForm.form.getFormValue("nodropzone",[]),dragNo=0;dragNo<numDrags;dragNo++){var label=dragDropToImageForm.form.getFormValue("draglabel",[dragNo]),file=dragDropToImageForm.fp.file(dragDropToImageForm.form.toNameWithIndex("dragitem",[dragNo]));"image"===dragDropToImageForm.form.getFormValue("drags",[dragNo,"dragitemtype"])&&null!==file.name?dragItemOptions[dragNo+1]=dragNo+1+". "+label+" ("+file.name+")":""!==label&&(dragItemOptions[dragNo+1]=dragNo+1+". "+label)}for(var dropNo=0;dropNo<numDrops;dropNo++){var selector=$("#id_drops_"+dropNo+"_choice"),selectedvalue=selector.val();for(var value in selector.find("option").remove(),dragItemOptions)if(dragItemOptions.hasOwnProperty(value)){selector.append('<option value="'+value+'">'+dragItemOptions[value]+"</option>");var optionnode=selector.find('option[value="'+value+'"]');parseInt(value)===parseInt(selectedvalue)?optionnode.attr("selected",!0):dragDropToImageForm.isItemUsed(parseInt(value))&&optionnode.attr("disabled",!0)}}},isItemUsed:function(value){return 0!==value&&(!dragDropToImageForm.form.getFormValue("drags",[value-1,"infinite"])&&0!==$('fieldset#id_dropzoneheader select[name^="drops"]').filter((function(i,selectNode){return parseInt($(selectNode).val())===value})).length)},dragStart:function(e){var drop=$(e.target).closest(".droppreview");dragDrop.prepare(e).start&&dragDrop.start(e,drop,(function(x,y,drop){dragDropToImageForm.dragMove(drop)}),(function(){dragDropToImageForm.dragEnd()}))},dragMove:function(drop){var backgroundImage=$("fieldset#id_previewareaheader .dropbackground"),backgroundPosition=backgroundImage.offset(),dropNo=drop.data("dropNo"),dropPosition=drop.offset(),left=Math.round(dropPosition.left-backgroundPosition.left),top=Math.round(dropPosition.top-backgroundPosition.top);left=Math.round(Math.max(0,Math.min(left,backgroundImage.outerWidth()-drop.outerWidth()))),top=Math.round(Math.max(0,Math.min(top,backgroundImage.outerHeight()-drop.outerHeight()))),dragDropToImageForm.form.setFormValue("drops",[dropNo,"xleft"],left),dragDropToImageForm.form.setFormValue("drops",[dropNo,"ytop"],top)},dragEnd:function(){dragDropToImageForm.updateDropZones()},form:{toNameWithIndex:function(name,indexes){for(var indexString=name,i=0;i<indexes.length;i++)indexString=indexString+"["+indexes[i]+"]";return indexString},getEl:function(name,indexes){return $('form.mform[data-qtype="ddimageortext"]')[0].elements[this.toNameWithIndex(name,indexes)]},getFormValue:function(name,indexes){var el=this.getEl(name,indexes);return el.type||(el=el[el.length-1]),"checkbox"===el.type?el.checked:el.value},setFormValue:function(name,indexes,value){var el=this.getEl(name,indexes);"checkbox"===el.type?el.checked=value:el.value=value}},filePickers:function(){var draftItemIdsToName,nameToParentNode;void 0===draftItemIdsToName&&(draftItemIdsToName={},nameToParentNode={},$('form.mform[data-qtype="ddimageortext"] input.filepickerhidden').each((function(index,filepicker){draftItemIdsToName[filepicker.value]=filepicker.name,nameToParentNode[filepicker.name]=filepicker.parentNode})));return{file:function(name){var fileAnchor=$(nameToParentNode[name]).find("div.filepicker-filelist a");return fileAnchor.length?{href:fileAnchor.get(0).href,name:fileAnchor.get(0).innerHTML}:{href:null,name:null}},name:function(draftitemid){return draftItemIdsToName[draftitemid]}}}};return{init:dragDropToImageForm.init}}));

//# sourceMappingURL=form.min.js.map