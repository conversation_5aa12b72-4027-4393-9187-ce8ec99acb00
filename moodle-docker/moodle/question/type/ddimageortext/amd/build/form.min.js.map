{"version": 3, "file": "form.min.js", "sources": ["../src/form.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/*\n * JavaScript to allow dragging options to slots (using mouse down or touch) or tab through slots using keyboard.\n *\n * @module     qtype_ddimageortext/form\n * @copyright  2018 The Open University\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine(['jquery', 'core/dragdrop'], function($, dragDrop) {\n\n    \"use strict\";\n\n    /**\n     * Singleton object to handle progressive enhancement of the\n     * drag-drop onto image question editing form.\n     * @type {Object}\n     */\n    var dragDropToImageForm = {\n        /**\n         * @var {Object} maxBgImageSize Properties width and height.\n         * @private\n         */\n        maxBgImageSize: null,\n\n        /**\n         * @var {Object} maxDragImageSize with properties width and height.\n         * @private\n         */\n        maxDragImageSize: null,\n\n        /**\n         * @property {object} fp for interacting with the file pickers.\n         * @private\n         */\n        fp: null, // Object containing functions associated with the file picker.\n\n        /**\n         * Initialise the form javascript features.\n         *\n         * @method\n         */\n        init: function() {\n            dragDropToImageForm.fp = dragDropToImageForm.filePickers();\n            dragDropToImageForm.updateVisibilityOfFilePickers();\n            dragDropToImageForm.setOptionsForDragItemSelectors();\n            dragDropToImageForm.setupEventHandlers();\n            dragDropToImageForm.waitForFilePickerToInitialise();\n        },\n\n        /**\n         * Add html for the preview area.\n         */\n        setupPreviewArea: function() {\n            $('#id_previewareaheader').append(\n                '<div class=\"ddarea que ddimageortext\">' +\n                '  <div id=\"id_droparea\" class=\"droparea\">' +\n                '    <img class=\"dropbackground\" />' +\n                '    <div class=\"dropzones\"></div>' +\n                '  </div>' +\n                '  <div class=\"dragitems\"></div>' +\n                '</div>');\n        },\n\n        /**\n         * Waits for the file-pickers to be sufficiently ready before initialising the preview.\n         */\n        waitForFilePickerToInitialise: function() {\n            if (dragDropToImageForm.fp.file('bgimage').href === null) {\n                // It would be better to use an onload or onchange event rather than this timeout.\n                // Unfortunately attempts to do this early are overwritten by filepicker during its loading.\n                setTimeout(dragDropToImageForm.waitForFilePickerToInitialise, 1000);\n                return;\n            }\n            M.util.js_pending('dragDropToImageForm');\n\n            // From now on, when a new file gets loaded into the filepicker, update the preview.\n            // This is not in the setupEventHandlers section as it needs to be delayed until\n            // after filepicker's javascript has finished.\n            $('form.mform[data-qtype=\"ddimageortext\"]').on('change', '.filepickerhidden', function() {\n                M.util.js_pending('dragDropToImageForm');\n                dragDropToImageForm.loadPreviewImage();\n            });\n            if ($('#id_droparea').length) {\n                dragDropToImageForm.loadPreviewImage();\n            } else {\n                // Setup preview area when the background image is uploaded the first time.\n                dragDropToImageForm.setupPreviewArea();\n                dragDropToImageForm.loadPreviewImage();\n            }\n        },\n\n        /**\n         * Loads the preview background image.\n         */\n        loadPreviewImage: function() {\n            $('fieldset#id_previewareaheader .dropbackground')\n                .one('load', dragDropToImageForm.afterPreviewImageLoaded)\n                .attr('src', dragDropToImageForm.fp.file('bgimage').href);\n        },\n\n        /**\n         * After the background image is loaded, continue setting up the preview.\n         */\n        afterPreviewImageLoaded: function() {\n            dragDropToImageForm.createDropZones();\n            M.util.js_complete('dragDropToImageForm');\n        },\n\n        /**\n         * Create, or recreate all the drop zones.\n         */\n        createDropZones: function() {\n            var dropZoneHolder = $('.dropzones');\n            dropZoneHolder.empty();\n\n            var bgimageurl = dragDropToImageForm.fp.file('bgimage').href;\n            if (bgimageurl === null) {\n                return; // There is not currently a valid preview to update.\n            }\n\n            var numDrops = dragDropToImageForm.form.getFormValue('nodropzone', []);\n            var dropzonevisibility = dragDropToImageForm.form.getFormValue('dropzonevisibility', []);\n            var dropzonevisibilitystyle = dropzonevisibility === '1' ? 'background: transparent;' : '';\n            for (var dropNo = 0; dropNo < numDrops; dropNo++) {\n                var dragNo = dragDropToImageForm.form.getFormValue('drops', [dropNo, 'choice']);\n                if (dragNo === '0') {\n                    continue;\n                }\n                dragNo = dragNo - 1;\n                var group = dragDropToImageForm.form.getFormValue('drags', [dragNo, 'draggroup']),\n                    label = dragDropToImageForm.form.getFormValue('draglabel', [dragNo]);\n                if ('image' === dragDropToImageForm.form.getFormValue('drags', [dragNo, 'dragitemtype'])) {\n                    var imgUrl = dragDropToImageForm.fp.file('dragitem[' + dragNo + ']').href;\n                    if (imgUrl === null) {\n                        continue;\n                    }\n                    // Althoug these are previews of drops, we also add the class name 'drag',\n                    dropZoneHolder.append('<img class=\"droppreview group' + group + ' drop' + dropNo +\n                        '\" src=\"' + imgUrl + '\" alt=\"' + label + '\" data-drop-no=\"' + dropNo +\n                        '\" style=\"' + dropzonevisibilitystyle + '\" >');\n\n                } else if (label !== '') {\n                    dropZoneHolder.append('<div class=\"droppreview group' + group + ' drop' + dropNo +\n                        '\"  data-drop-no=\"' + dropNo + '\">' + label + '</div>');\n                }\n            }\n\n            dragDropToImageForm.waitForAllDropImagesToBeLoaded();\n        },\n\n        /**\n         * This polls until all the drop-zone images have loaded, and then calls updateDropZones().\n         */\n        waitForAllDropImagesToBeLoaded: function() {\n            var notYetLoadedImages = $('.dropzones img').not(function(i, imgNode) {\n                return dragDropToImageForm.imageIsLoaded(imgNode);\n            });\n\n            if (notYetLoadedImages.length > 0) {\n                setTimeout(function() {\n                    dragDropToImageForm.waitForAllDropImagesToBeLoaded();\n                }, 100);\n                return;\n            }\n\n            dragDropToImageForm.updateDropZones();\n        },\n\n        /**\n         * Check if an image has loaded without errors.\n         *\n         * @param {HTMLImageElement} imgElement an image.\n         * @returns {boolean} true if this image has loaded without errors.\n         */\n        imageIsLoaded: function(imgElement) {\n            return imgElement.complete && imgElement.naturalHeight !== 0;\n        },\n\n        /**\n         * Set the size and position of all the drop zones.\n         */\n        updateDropZones: function() {\n            var bgimageurl = dragDropToImageForm.fp.file('bgimage').href;\n            if (bgimageurl === null) {\n                return; // There is not currently a valid preview to update.\n            }\n\n            var dropBackgroundPosition = $('fieldset#id_previewareaheader .dropbackground').offset(),\n                numDrops = dragDropToImageForm.form.getFormValue('nodropzone', []);\n\n            // Move each drop to the right position and update the text.\n            for (var dropNo = 0; dropNo < numDrops; dropNo++) {\n                var drop = $('.dropzones .drop' + dropNo);\n                if (drop.length === 0) {\n                    continue;\n                }\n                var dragNo = dragDropToImageForm.form.getFormValue('drops', [dropNo, 'choice']) - 1;\n\n                drop.offset({\n                    left: dropBackgroundPosition.left +\n                            parseInt(dragDropToImageForm.form.getFormValue('drops', [dropNo, 'xleft'])),\n                    top: dropBackgroundPosition.top +\n                            parseInt(dragDropToImageForm.form.getFormValue('drops', [dropNo, 'ytop']))\n                });\n\n                var label = dragDropToImageForm.form.getFormValue('draglabel', [dragNo]);\n                if (drop.is('img')) {\n                    drop.attr('alt', label);\n                } else {\n                    drop.html(label);\n                }\n            }\n\n            // Resize them to the same size.\n            $('.dropzones .droppreview').css('padding', '0');\n            var numGroups = $('.draggroup select').first().find('option').length;\n            for (var group = 1; group <= numGroups; group++) {\n                dragDropToImageForm.resizeAllDragsAndDropsInGroup(group);\n            }\n        },\n\n        /**\n         * In a given group, set all the drags and drops to be the same size.\n         *\n         * @param {int} group the group number.\n         */\n        resizeAllDragsAndDropsInGroup: function(group) {\n            var drops = $('.dropzones .droppreview.group' + group),\n                maxWidth = 0,\n                maxHeight = 0;\n\n            // Find the maximum size of any drag in this groups.\n            drops.each(function(i, drop) {\n                maxWidth = Math.max(maxWidth, Math.ceil(drop.offsetWidth));\n                maxHeight = Math.max(maxHeight, Math.ceil(drop.offsetHeight));\n            });\n\n            // The size we will want to set is a bit bigger than this.\n            maxWidth += 10;\n            maxHeight += 10;\n\n            // Set each drag home to that size.\n            drops.each(function(i, drop) {\n                var left = Math.round((maxWidth - drop.offsetWidth) / 2),\n                    top = Math.floor((maxHeight - drop.offsetHeight) / 2);\n                // Set top and left padding so the item is centred.\n                $(drop).css({\n                    'padding-left': left + 'px',\n                    'padding-right': (maxWidth - drop.offsetWidth - left) + 'px',\n                    'padding-top': top + 'px',\n                    'padding-bottom': (maxHeight - drop.offsetHeight - top) + 'px'\n                });\n            });\n        },\n\n        /**\n         * Events linked to form actions.\n         */\n        setupEventHandlers: function() {\n            // Changes to settings in the draggable items section.\n            $('fieldset#id_draggableitemheader')\n                .on('change input', 'input, select', function(e) {\n                    var input = $(e.target).closest('select, input');\n                    if (input.hasClass('dragitemtype')) {\n                        dragDropToImageForm.updateVisibilityOfFilePickers();\n                    }\n\n                    dragDropToImageForm.setOptionsForDragItemSelectors();\n\n                    if (input.is('.dragitemtype, .draggroup')) {\n                        dragDropToImageForm.createDropZones();\n                    } else if (input.is('.draglabel')) {\n                        dragDropToImageForm.updateDropZones();\n                    }\n                });\n\n            // Changes to Drop zones section: left, top and drag item.\n            $('fieldset#id_dropzoneheader').on('change input', 'input, select', function(e) {\n                var input = $(e.target).closest('select, input');\n                if (input.attr('id') === 'id_dropzonevisibility') {\n                    return;\n                }\n\n                if (input.is('select')) {\n                    dragDropToImageForm.createDropZones();\n                } else {\n                    dragDropToImageForm.updateDropZones();\n                }\n            });\n\n            // Moving drop zones in the preview.\n            $('fieldset#id_previewareaheader').on('mousedown touchstart', '.droppreview', function(e) {\n                dragDropToImageForm.dragStart(e);\n            });\n\n            $(window).on('resize', function() {\n                dragDropToImageForm.updateDropZones();\n            });\n\n            $('#id_dropzonevisibility').on('change', function() {\n                let selectedvalue = $(this).val();\n                if (selectedvalue === \"1\") {\n                    $('.droppreview').css('background', 'transparent');\n                } else if (selectedvalue === \"0\") {\n                    $('.droppreview').css('background', '');\n                }\n            });\n        },\n\n        /**\n         * Update all the drag item filepickers, so they are only shown for\n         */\n        updateVisibilityOfFilePickers: function() {\n            var numDrags = dragDropToImageForm.form.getFormValue('noitems', []);\n            for (var dragNo = 0; dragNo < numDrags; dragNo++) {\n                var picker = $('input#id_dragitem_' + dragNo).closest('.fitem_ffilepicker');\n                if ('image' === dragDropToImageForm.form.getFormValue('drags', [dragNo, 'dragitemtype'])) {\n                    picker.show();\n                } else {\n                    picker.hide();\n                }\n            }\n        },\n\n\n        setOptionsForDragItemSelectors: function() {\n            var dragItemOptions = {'0': ''},\n                numDrags = dragDropToImageForm.form.getFormValue('noitems', []),\n                numDrops = dragDropToImageForm.form.getFormValue('nodropzone', []);\n\n            // Work out the list of options.\n            for (var dragNo = 0; dragNo < numDrags; dragNo++) {\n                var label = dragDropToImageForm.form.getFormValue('draglabel', [dragNo]);\n                var file = dragDropToImageForm.fp.file(dragDropToImageForm.form.toNameWithIndex('dragitem', [dragNo]));\n                if ('image' === dragDropToImageForm.form.getFormValue('drags', [dragNo, 'dragitemtype']) && file.name !== null) {\n                    dragItemOptions[dragNo + 1] = (dragNo + 1) + '. ' + label + ' (' + file.name + ')';\n                } else if (label !== '') {\n                    dragItemOptions[dragNo + 1] = (dragNo + 1) + '. ' + label;\n                }\n            }\n\n            // Initialise each select.\n            for (var dropNo = 0; dropNo < numDrops; dropNo++) {\n                var selector = $('#id_drops_' + dropNo + '_choice');\n\n                var selectedvalue = selector.val();\n                selector.find('option').remove();\n                for (var value in dragItemOptions) {\n                    if (!dragItemOptions.hasOwnProperty(value)) {\n                        continue;\n                    }\n                    selector.append('<option value=\"' + value + '\">' + dragItemOptions[value] + '</option>');\n                    var optionnode = selector.find('option[value=\"' + value + '\"]');\n                    if (parseInt(value) === parseInt(selectedvalue)) {\n                        optionnode.attr('selected', true);\n                    } else if (dragDropToImageForm.isItemUsed(parseInt(value))) {\n                        optionnode.attr('disabled', true);\n                    }\n                }\n            }\n        },\n\n        /**\n         * Checks if the specified drag option is already used somewhere.\n         *\n         * @param {Number} value of the drag item to check\n         * @return {Boolean} true if item is allocated to dropzone\n         */\n        isItemUsed: function(value) {\n            if (value === 0) {\n                return false; // None option can always be selected.\n            }\n\n            if (dragDropToImageForm.form.getFormValue('drags', [value - 1, 'infinite'])) {\n                return false; // Infinite, so can't be used up.\n            }\n\n            return $('fieldset#id_dropzoneheader select[name^=\"drops\"]').filter(function(i, selectNode) {\n                return parseInt($(selectNode).val()) === value;\n            }).length !== 0;\n        },\n\n        /**\n         * Handles when a dropzone in dragged in the preview.\n         * @param {Object} e Event object\n         */\n        dragStart: function(e) {\n            var drop = $(e.target).closest('.droppreview');\n\n            var info = dragDrop.prepare(e);\n            if (!info.start) {\n                return;\n            }\n\n            dragDrop.start(e, drop, function(x, y, drop) {\n                dragDropToImageForm.dragMove(drop);\n            }, function() {\n                dragDropToImageForm.dragEnd();\n            });\n        },\n\n        /**\n         * Handles update while a drop is being dragged.\n         *\n         * @param {jQuery} drop the drop preview being moved.\n         */\n        dragMove: function(drop) {\n            var backgroundImage = $('fieldset#id_previewareaheader .dropbackground'),\n                backgroundPosition = backgroundImage.offset(),\n                dropNo = drop.data('dropNo'),\n                dropPosition = drop.offset(),\n                left = Math.round(dropPosition.left - backgroundPosition.left),\n                top = Math.round(dropPosition.top - backgroundPosition.top);\n\n            // Constrain coordinates to be inside the background.\n            left = Math.round(Math.max(0, Math.min(left, backgroundImage.outerWidth() - drop.outerWidth())));\n            top = Math.round(Math.max(0, Math.min(top, backgroundImage.outerHeight() - drop.outerHeight())));\n\n            // Update the form.\n            dragDropToImageForm.form.setFormValue('drops', [dropNo, 'xleft'], left);\n            dragDropToImageForm.form.setFormValue('drops', [dropNo, 'ytop'], top);\n        },\n\n        /**\n         * Handles when the drag ends.\n         */\n        dragEnd: function() {\n            // Redraw, in case the position was constrained.\n            dragDropToImageForm.updateDropZones();\n        },\n\n        /**\n         * Low level operations on form.\n         */\n        form: {\n            toNameWithIndex: function(name, indexes) {\n                var indexString = name;\n                for (var i = 0; i < indexes.length; i++) {\n                    indexString = indexString + '[' + indexes[i] + ']';\n                }\n                return indexString;\n            },\n\n            getEl: function(name, indexes) {\n                var form = $('form.mform[data-qtype=\"ddimageortext\"]')[0];\n                return form.elements[this.toNameWithIndex(name, indexes)];\n            },\n\n            /**\n             * Helper to get the value of a form elements with name like \"drops[0][xleft]\".\n             *\n             * @param {String} name the base name, e.g. 'drops'.\n             * @param {String[]} indexes the indexes, e.g. ['0', 'xleft'].\n             * @return {String} the value of that field.\n             */\n            getFormValue: function(name, indexes) {\n                var el = this.getEl(name, indexes);\n                if (!el.type) {\n                    el = el[el.length - 1];\n                }\n                if (el.type === 'checkbox') {\n                    return el.checked;\n                } else {\n                    return el.value;\n                }\n            },\n\n            /**\n             * Helper to get the value of a form elements with name like \"drops[0][xleft]\".\n             *\n             * @param {String} name the base name, e.g. 'drops'.\n             * @param {String[]} indexes the indexes, e.g. ['0', 'xleft'].\n             * @param {String|Number} value the value to set.\n             */\n            setFormValue: function(name, indexes, value) {\n                var el = this.getEl(name, indexes);\n                if (el.type === 'checkbox') {\n                    el.checked = value;\n                } else {\n                    el.value = value;\n                }\n            }\n        },\n\n        /**\n         * Utility to get the file name and url from the filepicker.\n         * @returns {Object} object containing functions {file, name}\n         */\n        filePickers: function() {\n            var draftItemIdsToName;\n            var nameToParentNode;\n\n            if (draftItemIdsToName === undefined) {\n                draftItemIdsToName = {};\n                nameToParentNode = {};\n                var fp = $('form.mform[data-qtype=\"ddimageortext\"] input.filepickerhidden');\n                fp.each(function(index, filepicker) {\n                    draftItemIdsToName[filepicker.value] = filepicker.name;\n                    nameToParentNode[filepicker.name] = filepicker.parentNode;\n                });\n            }\n\n            return {\n                file: function(name) {\n                    var parentNode = $(nameToParentNode[name]);\n                    var fileAnchor = parentNode.find('div.filepicker-filelist a');\n                    if (fileAnchor.length) {\n                        return {href: fileAnchor.get(0).href, name: fileAnchor.get(0).innerHTML};\n                    } else {\n                        return {href: null, name: null};\n                    }\n                },\n\n                name: function(draftitemid) {\n                    return draftItemIdsToName[draftitemid];\n                }\n            };\n        }\n    };\n\n    return {\n        init: dragDropToImageForm.init\n    };\n});\n"], "names": ["define", "$", "dragDrop", "dragDropToImageForm", "maxBgImageSize", "maxDragImageSize", "fp", "init", "filePickers", "updateVisibilityOfFilePickers", "setOptionsForDragItemSelectors", "setupEventHandlers", "waitForFilePickerToInitialise", "setupPreviewArea", "append", "file", "href", "M", "util", "js_pending", "on", "loadPreviewImage", "length", "setTimeout", "one", "afterPreviewImageLoaded", "attr", "createDropZones", "js_complete", "dropZoneHolder", "empty", "numDrops", "form", "getFormValue", "dropzonevisibilitystyle", "dropNo", "dragNo", "group", "label", "imgUrl", "waitForAllDropImagesToBeLoaded", "not", "i", "imgNode", "imageIsLoaded", "updateDropZones", "imgElement", "complete", "naturalHeight", "dropBackgroundPosition", "offset", "drop", "left", "parseInt", "top", "is", "html", "css", "numGroups", "first", "find", "resizeAllDragsAndDropsInGroup", "drops", "max<PERSON><PERSON><PERSON>", "maxHeight", "each", "Math", "max", "ceil", "offsetWidth", "offsetHeight", "round", "floor", "e", "input", "target", "closest", "hasClass", "dragStart", "window", "selectedvalue", "this", "val", "numDrags", "picker", "show", "hide", "dragItemOptions", "toNameWithIndex", "name", "selector", "value", "remove", "hasOwnProperty", "optionnode", "isItemUsed", "filter", "selectNode", "prepare", "start", "x", "y", "dragMove", "dragEnd", "backgroundImage", "backgroundPosition", "data", "dropPosition", "min", "outerWidth", "outerHeight", "setFormValue", "indexes", "indexString", "getEl", "elements", "el", "type", "checked", "draftItemIdsToName", "nameToParentNode", "undefined", "index", "filepicker", "parentNode", "fileAnchor", "get", "innerHTML", "draftitemid"], "mappings": ";;;;;;;AAsBAA,kCAAO,CAAC,SAAU,kBAAkB,SAASC,EAAGC,cASxCC,oBAAsB,CAKtBC,eAAgB,KAMhBC,iBAAkB,KAMlBC,GAAI,KAOJC,KAAM,WACFJ,oBAAoBG,GAAKH,oBAAoBK,cAC7CL,oBAAoBM,gCACpBN,oBAAoBO,iCACpBP,oBAAoBQ,qBACpBR,oBAAoBS,iCAMxBC,iBAAkB,WACdZ,EAAE,yBAAyBa,OACvB,oMAYRF,8BAA+B,WACyB,OAAhDT,oBAAoBG,GAAGS,KAAK,WAAWC,MAM3CC,EAAEC,KAAKC,WAAW,uBAKlBlB,EAAE,0CAA0CmB,GAAG,SAAU,qBAAqB,WAC1EH,EAAEC,KAAKC,WAAW,uBAClBhB,oBAAoBkB,sBAEpBpB,EAAE,gBAAgBqB,QAIlBnB,oBAAoBU,mBAHpBV,oBAAoBkB,oBAbpBE,WAAWpB,oBAAoBS,8BAA+B,MAwBtES,iBAAkB,WACdpB,EAAE,iDACGuB,IAAI,OAAQrB,oBAAoBsB,yBAChCC,KAAK,MAAOvB,oBAAoBG,GAAGS,KAAK,WAAWC,OAM5DS,wBAAyB,WACrBtB,oBAAoBwB,kBACpBV,EAAEC,KAAKU,YAAY,wBAMvBD,gBAAiB,eACTE,eAAiB5B,EAAE,iBACvB4B,eAAeC,QAGI,OADF3B,oBAAoBG,GAAGS,KAAK,WAAWC,cAKpDe,SAAW5B,oBAAoB6B,KAAKC,aAAa,aAAc,IAE/DC,wBAAiD,MAD5B/B,oBAAoB6B,KAAKC,aAAa,qBAAsB,IAC1B,2BAA6B,GAC/EE,OAAS,EAAGA,OAASJ,SAAUI,SAAU,KAC1CC,OAASjC,oBAAoB6B,KAAKC,aAAa,QAAS,CAACE,OAAQ,cACtD,MAAXC,QAGJA,QAAkB,MACdC,MAAQlC,oBAAoB6B,KAAKC,aAAa,QAAS,CAACG,OAAQ,cAChEE,MAAQnC,oBAAoB6B,KAAKC,aAAa,YAAa,CAACG,YAC5D,UAAYjC,oBAAoB6B,KAAKC,aAAa,QAAS,CAACG,OAAQ,iBAAkB,KAClFG,OAASpC,oBAAoBG,GAAGS,KAAK,YAAcqB,OAAS,KAAKpB,QACtD,OAAXuB,gBAIJV,eAAef,OAAO,gCAAkCuB,MAAQ,QAAUF,OACtE,UAAYI,OAAS,UAAYD,MAAQ,mBAAqBH,OAC9D,YAAcD,wBAA0B,WAE3B,KAAVI,OACPT,eAAef,OAAO,gCAAkCuB,MAAQ,QAAUF,OACtE,oBAAsBA,OAAS,KAAOG,MAAQ,WAI1DnC,oBAAoBqC,mCAMxBA,+BAAgC,WACHvC,EAAE,kBAAkBwC,KAAI,SAASC,EAAGC,gBAClDxC,oBAAoByC,cAAcD,YAGtBrB,OAAS,EAC5BC,YAAW,WACPpB,oBAAoBqC,mCACrB,KAIPrC,oBAAoB0C,mBASxBD,cAAe,SAASE,mBACbA,WAAWC,UAAyC,IAA7BD,WAAWE,eAM7CH,gBAAiB,cAEM,OADF1C,oBAAoBG,GAAGS,KAAK,WAAWC,cAKpDiC,uBAAyBhD,EAAE,iDAAiDiD,SAC5EnB,SAAW5B,oBAAoB6B,KAAKC,aAAa,aAAc,IAG1DE,OAAS,EAAGA,OAASJ,SAAUI,SAAU,KAC1CgB,KAAOlD,EAAE,mBAAqBkC,WACd,IAAhBgB,KAAK7B,YAGLc,OAASjC,oBAAoB6B,KAAKC,aAAa,QAAS,CAACE,OAAQ,WAAa,EAElFgB,KAAKD,OAAO,CACRE,KAAMH,uBAAuBG,KACrBC,SAASlD,oBAAoB6B,KAAKC,aAAa,QAAS,CAACE,OAAQ,WACzEmB,IAAKL,uBAAuBK,IACpBD,SAASlD,oBAAoB6B,KAAKC,aAAa,QAAS,CAACE,OAAQ,gBAGzEG,MAAQnC,oBAAoB6B,KAAKC,aAAa,YAAa,CAACG,SAC5De,KAAKI,GAAG,OACRJ,KAAKzB,KAAK,MAAOY,OAEjBa,KAAKK,KAAKlB,QAKlBrC,EAAE,2BAA2BwD,IAAI,UAAW,aACxCC,UAAYzD,EAAE,qBAAqB0D,QAAQC,KAAK,UAAUtC,OACrDe,MAAQ,EAAGA,OAASqB,UAAWrB,QACpClC,oBAAoB0D,8BAA8BxB,SAS1DwB,8BAA+B,SAASxB,WAChCyB,MAAQ7D,EAAE,gCAAkCoC,OAC5C0B,SAAW,EACXC,UAAY,EAGhBF,MAAMG,MAAK,SAASvB,EAAGS,MACnBY,SAAWG,KAAKC,IAAIJ,SAAUG,KAAKE,KAAKjB,KAAKkB,cAC7CL,UAAYE,KAAKC,IAAIH,UAAWE,KAAKE,KAAKjB,KAAKmB,kBAInDP,UAAY,GACZC,WAAa,GAGbF,MAAMG,MAAK,SAASvB,EAAGS,UACfC,KAAOc,KAAKK,OAAOR,SAAWZ,KAAKkB,aAAe,GAClDf,IAAMY,KAAKM,OAAOR,UAAYb,KAAKmB,cAAgB,GAEvDrE,EAAEkD,MAAMM,IAAI,gBACQL,KAAO,qBACLW,SAAWZ,KAAKkB,YAAcjB,KAAQ,mBACzCE,IAAM,sBACFU,UAAYb,KAAKmB,aAAehB,IAAO,WAQtE3C,mBAAoB,WAEhBV,EAAE,mCACGmB,GAAG,eAAgB,iBAAiB,SAASqD,OACtCC,MAAQzE,EAAEwE,EAAEE,QAAQC,QAAQ,iBAC5BF,MAAMG,SAAS,iBACf1E,oBAAoBM,gCAGxBN,oBAAoBO,iCAEhBgE,MAAMnB,GAAG,6BACTpD,oBAAoBwB,kBACb+C,MAAMnB,GAAG,eAChBpD,oBAAoB0C,qBAKhC5C,EAAE,8BAA8BmB,GAAG,eAAgB,iBAAiB,SAASqD,OACrEC,MAAQzE,EAAEwE,EAAEE,QAAQC,QAAQ,iBACP,0BAArBF,MAAMhD,KAAK,QAIXgD,MAAMnB,GAAG,UACTpD,oBAAoBwB,kBAEpBxB,oBAAoB0C,sBAK5B5C,EAAE,iCAAiCmB,GAAG,uBAAwB,gBAAgB,SAASqD,GACnFtE,oBAAoB2E,UAAUL,MAGlCxE,EAAE8E,QAAQ3D,GAAG,UAAU,WACnBjB,oBAAoB0C,qBAGxB5C,EAAE,0BAA0BmB,GAAG,UAAU,eACjC4D,cAAgB/E,EAAEgF,MAAMC,MACN,MAAlBF,cACA/E,EAAE,gBAAgBwD,IAAI,aAAc,eACX,MAAlBuB,eACP/E,EAAE,gBAAgBwD,IAAI,aAAc,QAQhDhD,8BAA+B,mBACvB0E,SAAWhF,oBAAoB6B,KAAKC,aAAa,UAAW,IACvDG,OAAS,EAAGA,OAAS+C,SAAU/C,SAAU,KAC1CgD,OAASnF,EAAE,qBAAuBmC,QAAQwC,QAAQ,sBAClD,UAAYzE,oBAAoB6B,KAAKC,aAAa,QAAS,CAACG,OAAQ,iBACpEgD,OAAOC,OAEPD,OAAOE,SAMnB5E,+BAAgC,mBACxB6E,gBAAkB,GAAM,IACxBJ,SAAWhF,oBAAoB6B,KAAKC,aAAa,UAAW,IAC5DF,SAAW5B,oBAAoB6B,KAAKC,aAAa,aAAc,IAG1DG,OAAS,EAAGA,OAAS+C,SAAU/C,SAAU,KAC1CE,MAAQnC,oBAAoB6B,KAAKC,aAAa,YAAa,CAACG,SAC5DrB,KAAOZ,oBAAoBG,GAAGS,KAAKZ,oBAAoB6B,KAAKwD,gBAAgB,WAAY,CAACpD,UACzF,UAAYjC,oBAAoB6B,KAAKC,aAAa,QAAS,CAACG,OAAQ,kBAAkC,OAAdrB,KAAK0E,KAC7FF,gBAAgBnD,OAAS,GAAMA,OAAS,EAAK,KAAOE,MAAQ,KAAOvB,KAAK0E,KAAO,IAC9D,KAAVnD,QACPiD,gBAAgBnD,OAAS,GAAMA,OAAS,EAAK,KAAOE,WAKvD,IAAIH,OAAS,EAAGA,OAASJ,SAAUI,SAAU,KAC1CuD,SAAWzF,EAAE,aAAekC,OAAS,WAErC6C,cAAgBU,SAASR,UAExB,IAAIS,SADTD,SAAS9B,KAAK,UAAUgC,SACNL,mBACTA,gBAAgBM,eAAeF,QAGpCD,SAAS5E,OAAO,kBAAoB6E,MAAQ,KAAOJ,gBAAgBI,OAAS,iBACxEG,WAAaJ,SAAS9B,KAAK,iBAAmB+B,MAAQ,MACtDtC,SAASsC,SAAWtC,SAAS2B,eAC7Bc,WAAWpE,KAAK,YAAY,GACrBvB,oBAAoB4F,WAAW1C,SAASsC,SAC/CG,WAAWpE,KAAK,YAAY,MAY5CqE,WAAY,SAASJ,cACH,IAAVA,SAIAxF,oBAAoB6B,KAAKC,aAAa,QAAS,CAAC0D,MAAQ,EAAG,cAMjD,IAFP1F,EAAE,oDAAoD+F,QAAO,SAAStD,EAAGuD,mBACrE5C,SAASpD,EAAEgG,YAAYf,SAAWS,SAC1CrE,SAOPwD,UAAW,SAASL,OACZtB,KAAOlD,EAAEwE,EAAEE,QAAQC,QAAQ,gBAEpB1E,SAASgG,QAAQzB,GAClB0B,OAIVjG,SAASiG,MAAM1B,EAAGtB,MAAM,SAASiD,EAAGC,EAAGlD,MACnChD,oBAAoBmG,SAASnD,SAC9B,WACChD,oBAAoBoG,cAS5BD,SAAU,SAASnD,UACXqD,gBAAkBvG,EAAE,iDACpBwG,mBAAqBD,gBAAgBtD,SACrCf,OAASgB,KAAKuD,KAAK,UACnBC,aAAexD,KAAKD,SACpBE,KAAOc,KAAKK,MAAMoC,aAAavD,KAAOqD,mBAAmBrD,MACzDE,IAAMY,KAAKK,MAAMoC,aAAarD,IAAMmD,mBAAmBnD,KAG3DF,KAAOc,KAAKK,MAAML,KAAKC,IAAI,EAAGD,KAAK0C,IAAIxD,KAAMoD,gBAAgBK,aAAe1D,KAAK0D,gBACjFvD,IAAMY,KAAKK,MAAML,KAAKC,IAAI,EAAGD,KAAK0C,IAAItD,IAAKkD,gBAAgBM,cAAgB3D,KAAK2D,iBAGhF3G,oBAAoB6B,KAAK+E,aAAa,QAAS,CAAC5E,OAAQ,SAAUiB,MAClEjD,oBAAoB6B,KAAK+E,aAAa,QAAS,CAAC5E,OAAQ,QAASmB,MAMrEiD,QAAS,WAELpG,oBAAoB0C,mBAMxBb,KAAM,CACFwD,gBAAiB,SAASC,KAAMuB,iBACxBC,YAAcxB,KACT/C,EAAI,EAAGA,EAAIsE,QAAQ1F,OAAQoB,IAChCuE,YAAcA,YAAc,IAAMD,QAAQtE,GAAK,WAE5CuE,aAGXC,MAAO,SAASzB,KAAMuB,gBACP/G,EAAE,0CAA0C,GAC3CkH,SAASlC,KAAKO,gBAAgBC,KAAMuB,WAUpD/E,aAAc,SAASwD,KAAMuB,aACrBI,GAAKnC,KAAKiC,MAAMzB,KAAMuB,gBACrBI,GAAGC,OACJD,GAAKA,GAAGA,GAAG9F,OAAS,IAER,aAAZ8F,GAAGC,KACID,GAAGE,QAEHF,GAAGzB,OAWlBoB,aAAc,SAAStB,KAAMuB,QAASrB,WAC9ByB,GAAKnC,KAAKiC,MAAMzB,KAAMuB,SACV,aAAZI,GAAGC,KACHD,GAAGE,QAAU3B,MAEbyB,GAAGzB,MAAQA,QASvBnF,YAAa,eACL+G,mBACAC,sBAEuBC,IAAvBF,qBACAA,mBAAqB,GACrBC,iBAAmB,GACVvH,EAAE,iEACRgE,MAAK,SAASyD,MAAOC,YACpBJ,mBAAmBI,WAAWhC,OAASgC,WAAWlC,KAClD+B,iBAAiBG,WAAWlC,MAAQkC,WAAWC,qBAIhD,CACH7G,KAAM,SAAS0E,UAEPoC,WADa5H,EAAEuH,iBAAiB/B,OACR7B,KAAK,oCAC7BiE,WAAWvG,OACJ,CAACN,KAAM6G,WAAWC,IAAI,GAAG9G,KAAMyE,KAAMoC,WAAWC,IAAI,GAAGC,WAEvD,CAAC/G,KAAM,KAAMyE,KAAM,OAIlCA,KAAM,SAASuC,oBACJT,mBAAmBS,uBAMnC,CACHzH,KAAMJ,oBAAoBI"}