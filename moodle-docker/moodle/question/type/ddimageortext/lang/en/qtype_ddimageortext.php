<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language file Drag and Drop image or text.
 *
 * @package   qtype_ddimageortext
 * @copyright 2011 The Open University
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['addmoredropzones'] = 'Blanks for {no} more drop zones';
$string['addmoreimages'] = 'Blanks for {no} more draggable items';
$string['answer'] = 'Answer';
$string['bgimage'] = 'Background image';
$string['blank'] = 'blank';
$string['correctansweris'] = 'The correct answer is: {$a}';
$string['deletedchoice'] = '[Deleted choice]';
$string['draggableimage'] = 'Draggable image';
$string['draggableitem'] = 'Draggable item';
$string['draggableitems'] = 'Draggable items';
$string['draggableitemheader'] = 'Draggable item {$a}';
$string['draggableitemtype'] = 'Type';
$string['draggableword'] = 'Draggable text';
$string['dropbackground'] = 'Background image for dragging markers onto';
$string['dropzone'] = 'Drop zone {$a}';
$string['dropzoneheader'] = 'Drop zones';
$string['dropzonevisibility'] = 'Drop zone visibility';
$string['dropzonevisibility_help'] = 'Should drop zones be transparent so they don\'t cover up parts of the background image? Draggable items will snap to nearby drop zones.';
$string['dropzonevisibility_hideoption'] = 'Transparent drop zones';
$string['dropzonevisibility_showoption'] = 'Show drop zones';
$string['dropzonevisibilitydesc'] = 'Borders will still be visible when editing the question to help with setting the drop zone.';
$string['formerror_disallowedtags'] = 'Only "{$a}" tags are allowed in this draggable text.';
$string['formerror_dragrequired'] = 'You must add at least one draggable item to this question.';
$string['formerror_droprequired'] = 'You must define at least one drop zone for this question.';
$string['formerror_noallowedtags'] = 'HTML tags are not allowed in this text which is the alt text for a draggable image.';
$string['formerror_noytop'] = 'You must provide a value for the y coordinate for the top left corner of this drop area. You can drag and drop the drop area above to set the coordinates or enter them manually here.';
$string['formerror_noxleft'] = 'You must provide a value for the x coordinate for the top left corner of this drop area. You can drag and drop the drop area above to set the coordinates or enter them manually here.';
$string['formerror_nofile'] = 'You need to upload or select a file to use here.';
$string['formerror_nofile3'] = 'You need to select an image file here, or delete the associated label and uncheck the unlimited checkbox.';
$string['formerror_notintytop'] = 'The y coordinate must be an integer.';
$string['formerror_notintxleft'] = 'The x coordinate must be an integer.';
$string['formerror_multipledraginstance'] = 'You have selected this image {$a} more than once as the correct choice for a drop zone but it is not marked as being an unlimited drag item.';
$string['formerror_multipledraginstance2'] = 'You have selected this image more than once as the correct choice for a drop zone but it is not marked as being an unlimited drag item.';
$string['formerror_noimageselected'] = 'You need to select a drag item to be the correct choice for this drop zone.';
$string['formerror_nobgimage'] = 'You need to select an image to use as the background for the drag and drop area.';
$string['infinite'] = 'Unlimited';
$string['label'] = 'Text';
$string['nolabel'] = 'No label text';
$string['pleasedraganimagetoeachdropregion'] = 'Your answer is not complete; please drag an item to each drop region.';
$string['pluginname'] = 'Drag and drop onto image';
$string['pluginname_help'] = 'Drag and drop onto image questions require the respondent to drag images or text labels and drop them into defined drop zones on a background image. Draggable items may be grouped so that all items in a particular group are coloured the same as the drop zones. Draggable items marked as unlimited may be dropped into more than one zone.';
$string['pluginname_link'] = 'question/type/ddimageortext';
$string['pluginnameadding'] = 'Adding drag and drop onto image';
$string['pluginnameediting'] = 'Editing drag and drop onto image';
$string['pluginnamesummary'] = 'Images or text labels are dragged and dropped into drop zones on a background image.

Note: This question type is not accessible to users who are visually impaired.';
$string['previewareaheader'] = 'Preview';
$string['previewareamessage'] = 'Select a background image, specify draggable items and define drop zones on the background image into which they must be dragged.';
$string['privacy:metadata'] = 'Drag and drop onto image question type plugin allows question authors to set default options as user preferences.';
$string['privacy:preference:defaultmark'] = 'The default mark set for a given question.';
$string['privacy:preference:penalty'] = 'The penalty for each incorrect try when questions are run using the \'Interactive with multiple tries\' or \'Adaptive mode\' behaviour.';
$string['privacy:preference:shuffleanswers'] = 'Whether the answers should be automatically shuffled.';
$string['refresh'] = 'Refresh preview';
$string['shuffleimages'] = 'Shuffle drag items each time question is attempted';
$string['summarisechoice'] = '{$a->no}. {$a->text}';
$string['summariseplace'] = '{$a->no}. {$a->text}';
$string['summarisechoiceno'] = 'Item {$a}';
$string['summariseplaceno'] = 'Drop zone {$a}';
$string['xleft'] = 'Left';
$string['ytop'] = 'Top';
