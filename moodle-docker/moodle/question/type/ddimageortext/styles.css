.que.ddimageortext .qtext {
    margin-bottom: 0.5em;
    display: block;
}

.que.ddimageortext div.ddarea,
form.mform fieldset#id_previewareaheader div.ddarea {
    text-align: center;
}

.que.ddimageortext div.droparea,
form.mform fieldset#id_previewareaheader div.ddarea {
    position: relative;
}

.que.ddimageortext div.droparea {
    display: inline-block;
}

.que.ddimageortext div.droparea .draghome {
    position: absolute;
    cursor: move;
    white-space: nowrap;
}

.que.ddimageortext div.droparea .dropzones {
    position: absolute;
    top: 0;
    /*rtl:ignore*/
    left: 0;
}

.que.ddimageortext .dropbackground,
form.mform fieldset#id_previewareaheader .dropbackground {
    border: 1px solid #000;
    margin: 0 auto;
}

form.mform fieldset#id_previewareaheader .dropbackground {
    max-width: none;
}

.que.ddimageortext .dropzone {
    display: none;
    position: absolute;
    opacity: 0.5;
    border: 1px solid black;
}

.que.ddimageortext .dropzone.active {
    display: block;
}

.que.ddimageortext .dropzone:focus,
.que.ddimageortext .droparea .draghome:focus,
.que.ddimageortext .dropzone.valid-drag-over-drop,
.que.ddimageortext .draghome.placed.valid-drag-over-drop {
    border-color: #0a0;
    box-shadow: 0 0 5px 5px rgba(255, 255, 150, 1);
    outline: 0;
}

.que.ddimageortext .draghome,
.que.ddimageortext .drag,
form.mform fieldset#id_previewareaheader .droppreview {
    border: 1px solid black;
    display: inline-block;
    font: 13px/1.231 arial, helvetica, clean, sans-serif;
}

.que.ddimageortext .draghomes .draghome {
    vertical-align: top;
    margin: 5px;
    height: auto;
    width: auto;
    cursor: move;
}

.que.ddimageortext .draghomes.readonly .draghome,
.que.ddimageortext .droparea.readonly .draghome {
    cursor: auto;
}

.que.ddimageortext .draghomes .draghome.dragplaceholder {
    display: none;
}

/**
 * The position of mathjax element in ddimageortext is calculated by javascript in question.js line 254.
 * So we remove the default margin of Mathjax.
 */
.que.ddimageortext .MathJax_Display,
.que.ddimageortext .MathJax_SVG {
    margin: 0;
}

.que.ddimageortext .draghomes .draghome.dragplaceholder.active {
    visibility: hidden;
    display: inline-block;
}

.que.ddimageortext .dragitems,
form.mform fieldset#id_previewareaheader .dragitems {
    height: 0;
}

.que.ddimageortext .drag,
form.mform fieldset#id_previewareaheader .droppreview {
    position: absolute;
    cursor: move;
    white-space: nowrap;
}

.que.ddimageortext .dragitems.readonly .drag {
    cursor: auto;
}

form.mform fieldset#id_previewareaheader .drag.beingdragged,
.que.ddimageortext .drag.beingdragged,
.que.ddimageortext .draghomes .draghome.beingdragged,
.que.ddimageortext .droparea .draghome.beingdragged {
    box-shadow: 3px 3px 4px #000;
}

.que.ddimageortext .draghomes .draghome.beingdragged,
.que.ddimageortext .droparea .draghome.beingdragged {
    position: absolute;
}

.que.ddimageortext .group1,
form.mform fieldset#id_previewareaheader .group1 {
    background-color: #fff;
}

.que.ddimageortext .transparent .dropzones > img {
    border: none;
}

.que.ddimageortext .transparent .dropzones > img:hover {
    border: 1px solid #000;
}

.que.ddimageortext .transparent img:not(.dropbackground):hover,
.que.ddimageortext .droparea.transparent .dropzone,
.que.ddimageortext .droparea.transparent .dropzone + img {
    background-color: transparent;
}

.que.ddimageortext .group2,
form.mform fieldset#id_previewareaheader .group2 {
    background-color: #b0c4de;
    border-radius: 10px 0 0 0;
}

.que.ddimageortext .group3,
form.mform fieldset#id_previewareaheader .group3 {
    background-color: #dcdcdc;
    border-radius: 0 10px 0 0;
}

.que.ddimageortext .group4,
form.mform fieldset#id_previewareaheader .group4 {
    background-color: #d8bfd8;
    border-radius: 0 0 10px 0;
}

.que.ddimageortext .group5,
form.mform fieldset#id_previewareaheader .group5 {
    background-color: #87cefa;
    border-radius: 0 0 0 10px;
}

.que.ddimageortext .group6,
form.mform fieldset#id_previewareaheader .group6 {
    background-color: #daa520;
    border-radius: 0 10px 10px 0;
}

.que.ddimageortext .group7,
form.mform fieldset#id_previewareaheader .group7 {
    background-color: #ffd700;
    border-radius: 10px 0 0 10px;
}

.que.ddimageortext .group8,
form.mform fieldset#id_previewareaheader .group8 {
    background-color: #f0e68c;
    border-radius: 10px 10px 10px 10px;
}

/* Editing form. Style repeated elements*/
/*Top*/
body#page-question-type-ddimageortext div[id^=fgroup_id_][id*=drags_] {
    background: #eee;
    margin-top: 0;
    margin-bottom: 0;
    padding-bottom: 5px;
    padding-top: 5px;
    border: 1px solid #bbb;
    border-bottom: 0;
}

body#page-question-type-ddimageortext div[id^=fgroup_id_][id*=drags_] .fgrouplabel label {
    font-weight: bold;
}
/* Middle */
body#page-question-type-ddimageortext div[id^=fitem_id_][id*=dragitem_] {
    background: #eee;
    margin-bottom: 0;
    margin-top: 0;
    padding-bottom: 5px;
    padding-top: 5px;
    border: 1px solid #bbb;
    border-top: 0;
    border-bottom: 0;
}
/* Bottom */
body#page-question-type-ddimageortext div[id^=fitem_id_][id*=draglabel_] {
    background: #eee;
    margin-bottom: 2em;
    margin-top: 0;
    padding-bottom: 5px;
    padding-top: 5px;
    border: 1px solid #bbb;
    border-top: 0;
}

/* stylelint-disable declaration-no-important  */

div[id^="fgroup_id_drags_"].mb-3,
div[id^="fitem_id_dragitem_"].mb-3 {
    margin-bottom: 0 !important;
}

/* stylelint-enable declaration-no-important  */

