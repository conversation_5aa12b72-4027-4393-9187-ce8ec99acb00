/**
 * JavaScript to make drag-drop into text questions work.
 *
 * Some vocabulary to help understand this code:
 *
 * The question text contains 'drops' - blanks into which the 'drags', the missing
 * words, can be put.
 *
 * The thing that can be moved into the drops are called 'drags'. There may be
 * multiple copies of the 'same' drag which does not really cause problems.
 * Each drag has a 'choice' number which is the value set on the drop's hidden
 * input when this drag is placed in a drop.
 *
 * These may be in separate 'groups', distinguished by colour.
 * Things can only interact with other things in the same group.
 * The groups are numbered from 1.
 *
 * The place where a given drag started from is called its 'home'.
 *
 * @module     qtype_ddwtos/ddwtos
 * @copyright  2018 The Open University
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.6
 */
define("qtype_ddwtos/ddwtos",["jquery","core/dragdrop","core/key_codes","core_form/changechecker","core_filters/events"],(function($,dragDrop,keys,FormChangeChecker,filterEvent){function DragDropToTextQuestion(containerId,readOnly){const thisQ=this;this.containerId=containerId,this.questionAnswer={},this.questionDragDropWidthHeight=[],readOnly&&this.getRoot().addClass("qtype_ddwtos-readonly"),this.resizeAllDragsAndDrops(),this.cloneDrags(),this.positionDrags(),document.addEventListener(filterEvent.eventTypes.filterContentRenderingComplete,(elements=>{elements.detail.nodes.forEach((element=>{thisQ.changeAllDragsAndDropsToFilteredContent(element)}))}))}DragDropToTextQuestion.prototype.resizeAllDragsAndDrops=function(){var thisQ=this;this.getRoot().find(".answercontainer > div").each((function(i,node){thisQ.resizeAllDragsAndDropsInGroup(thisQ.getClassnameNumericSuffix($(node),"draggrouphomes"))}))},DragDropToTextQuestion.prototype.resizeAllDragsAndDropsInGroup=function(group){var thisQ=this,dragDropItems=this.getRoot().find("span.group"+group),maxWidth=0,maxHeight=0;dragDropItems.each((function(i,drag){maxWidth=Math.max(maxWidth,Math.ceil(drag.offsetWidth)),maxHeight=Math.max(maxHeight,Math.ceil(0+drag.offsetHeight))})),maxWidth+=8,maxHeight+=2,thisQ.questionDragDropWidthHeight[group]={maxWidth:maxWidth,maxHeight:maxHeight},dragDropItems.each((function(i,drag){thisQ.setElementSize(drag,maxWidth,maxHeight)}))},DragDropToTextQuestion.prototype.changeAllDragsAndDropsToFilteredContent=function(filteredElement){let currentFilteredItem=$(filteredElement);const parentIsDD=currentFilteredItem.parent().closest("span").hasClass("placed")||currentFilteredItem.parent().closest("span").hasClass("draghome"),isDD=currentFilteredItem.hasClass("placed")||currentFilteredItem.hasClass("draghome");if(!parentIsDD&&!isDD)return;parentIsDD&&(currentFilteredItem=currentFilteredItem.parent().closest("span"));const thisQ=this;if(thisQ.getRoot().find(currentFilteredItem).length<=0)return;const group=thisQ.getGroup(currentFilteredItem),choice=thisQ.getChoice(currentFilteredItem);let listOfModifiedDragDrop=[];this.getRoot().find(".group"+group+".choice"+choice).each((function(i,node){if($(node).get(0)===currentFilteredItem.get(0))return;const originalClass=$(node).attr("class"),originalStyle=$(node).attr("style"),filteredDragDropClone=currentFilteredItem.clone();filteredDragDropClone.attr("class",originalClass),filteredDragDropClone.attr("style",originalStyle),$(node).before(filteredDragDropClone),listOfModifiedDragDrop.push(node)})),listOfModifiedDragDrop.forEach((function(node){$(node).remove()}));const currentHeight=currentFilteredItem.height(),currentWidth=currentFilteredItem.width();currentFilteredItem.height("auto"),currentFilteredItem.width("auto"),filteredElement.offsetWidth&&filteredElement.offsetHeight||filteredElement.classList.add("d-block"),thisQ.questionDragDropWidthHeight[group].maxWidth<Math.ceil(filteredElement.offsetWidth)||thisQ.questionDragDropWidthHeight[group].maxHeight<Math.ceil(0+filteredElement.offsetHeight)?(filteredElement.classList.remove("d-block"),thisQ.resizeAllDragsAndDropsInGroup(group)):(currentFilteredItem.height(currentHeight),currentFilteredItem.width(currentWidth)),filteredElement.classList.remove("d-block")},DragDropToTextQuestion.prototype.setElementSize=function(element,width,height){$(element).width(width).height(height).css("lineHeight",height+"px")},DragDropToTextQuestion.prototype.cloneDrags=function(){var thisQ=this;thisQ.getRoot().find("span.draghome").each((function(index,draghome){var drag=$(draghome),placeHolder=drag.clone();placeHolder.removeClass(),placeHolder.addClass("draghome choice"+thisQ.getChoice(drag)+" group"+thisQ.getGroup(drag)+" dragplaceholder"),drag.before(placeHolder)}))},DragDropToTextQuestion.prototype.positionDrags=function(){var thisQ=this,root=this.getRoot();root.find("span.draghome").not(".dragplaceholder").each((function(i,dragNode){var drag=$(dragNode),currentPlace=thisQ.getClassnameNumericSuffix(drag,"inplace");drag.addClass("unplaced").removeClass("placed"),drag.removeAttr("tabindex"),null!==currentPlace&&drag.removeClass("inplace"+currentPlace)})),root.find("input.placeinput").each((function(i,inputNode){var input=$(inputNode),choice=input.val(),place=thisQ.getPlace(input),drop=root.find(".drop.place"+place),dropPosition=drop.offset();if(drop.data("prev-top",dropPosition.top).data("prev-left",dropPosition.left),"0"!==choice){var unplacedDrag=thisQ.getUnplacedChoice(thisQ.getGroup(input),choice),hiddenDrag=thisQ.getDragClone(unplacedDrag);if(hiddenDrag.length)if(unplacedDrag.hasClass("infinite")){var noOfDrags=thisQ.noOfDropsInGroup(thisQ.getGroup(unplacedDrag));if(thisQ.getInfiniteDragClones(unplacedDrag,!1).length<noOfDrags){var cloneDrag=unplacedDrag.clone();hiddenDrag.after(cloneDrag),questionManager.addEventHandlersToDrag(cloneDrag)}else hiddenDrag.addClass("active")}else hiddenDrag.addClass("active");thisQ.sendDragToDrop(thisQ.getUnplacedChoice(thisQ.getGroup(input),choice),drop)}})),thisQ.questionAnswer=thisQ.getQuestionAnsweredValues()},DragDropToTextQuestion.prototype.getQuestionAnsweredValues=function(){let result={};return this.getRoot().find("input.placeinput").each(((i,inputNode)=>{result[inputNode.id]=inputNode.value})),result},DragDropToTextQuestion.prototype.isQuestionInteracted=function(){const oldAnswer=this.questionAnswer,newAnswer=this.getQuestionAnsweredValues();let isInteracted=!1;return JSON.stringify(newAnswer)!==JSON.stringify(oldAnswer)?(isInteracted=!0,isInteracted):(Object.keys(newAnswer).forEach((key=>{newAnswer[key]!==oldAnswer[key]&&(isInteracted=!0)})),isInteracted)},DragDropToTextQuestion.prototype.handleDragStart=function(e){var thisQ=this,drag=$(e.target).closest(".draghome");if(dragDrop.prepare(e).start&&!drag.hasClass("beingdragged")){drag.addClass("beingdragged");var currentPlace=this.getClassnameNumericSuffix(drag,"inplace");if(null!==currentPlace){this.setInputValue(currentPlace,0),drag.removeClass("inplace"+currentPlace);var hiddenDrop=thisQ.getDrop(drag,currentPlace);hiddenDrop.length&&(hiddenDrop.addClass("active"),drag.offset(hiddenDrop.offset()))}else{var hiddenDrag=thisQ.getDragClone(drag);if(hiddenDrag.length)if(drag.hasClass("infinite")){var noOfDrags=this.noOfDropsInGroup(this.getGroup(drag));if(this.getInfiniteDragClones(drag,!1).length<noOfDrags){var cloneDrag=drag.clone();cloneDrag.removeClass("beingdragged"),hiddenDrag.after(cloneDrag),questionManager.addEventHandlersToDrag(cloneDrag),drag.offset(cloneDrag.offset())}else hiddenDrag.addClass("active"),drag.offset(hiddenDrag.offset())}else hiddenDrag.addClass("active"),drag.offset(hiddenDrag.offset())}dragDrop.start(e,drag,(function(x,y,drag){thisQ.dragMove(x,y,drag)}),(function(x,y,drag){thisQ.dragEnd(x,y,drag)}))}},DragDropToTextQuestion.prototype.dragMove=function(pageX,pageY,drag){var thisQ=this;this.getRoot().find("span.group"+this.getGroup(drag)).not(".beingdragged").each((function(i,dropNode){var drop=$(dropNode);thisQ.isPointInDrop(pageX,pageY,drop)?drop.addClass("valid-drag-over-drop"):drop.removeClass("valid-drag-over-drop")}))},DragDropToTextQuestion.prototype.dragEnd=function(pageX,pageY,drag){var thisQ=this,root=this.getRoot(),placed=!1;root.find("span.group"+this.getGroup(drag)).not(".beingdragged").each((function(i,dropNode){if(placed)return!1;const dropZone=$(dropNode);if(!thisQ.isPointInDrop(pageX,pageY,dropZone))return!0;let drop=null;return dropZone.hasClass("placed")?(dropZone.removeClass("valid-drag-over-drop"),drop=thisQ.getDrop(drag,thisQ.getClassnameNumericSuffix(dropZone,"inplace"))):drop=dropZone,drop.removeClass("valid-drag-over-drop"),thisQ.sendDragToDrop(drag,drop),placed=!0,!1})),placed||this.sendDragHome(drag)},DragDropToTextQuestion.prototype.sendDragToDrop=function(drag,drop){if(null!==this.getPlace(drop)){var oldDrag=this.getCurrentDragInPlace(this.getPlace(drop));if(0!==oldDrag.length){var currentPlace=this.getClassnameNumericSuffix(oldDrag,"inplace");if(this.hasDropSameDrag(currentPlace,drop,oldDrag,drag))return void this.sendDragHome(drag);var hiddenDrop=this.getDrop(oldDrag,currentPlace);hiddenDrop.addClass("active"),oldDrag.addClass("beingdragged"),oldDrag.offset(hiddenDrop.offset()),this.sendDragHome(oldDrag)}if(0===drag.length)this.setInputValue(this.getPlace(drop),0),drop.data("isfocus")&&drop.focus();else{if(this.getClassnameNumericSuffix(drag,"inplace"))return;this.setInputValue(this.getPlace(drop),this.getChoice(drag)),drag.removeClass("unplaced").addClass("placed inplace"+this.getPlace(drop)),drag.attr("tabindex",0),this.animateTo(drag,drop)}}else this.sendDragHome(drag)},DragDropToTextQuestion.prototype.hasDropSameDrag=function(currentPlace,drop,oldDrag,drag){return!!drag.hasClass("infinite")&&(drop.hasClass("place"+currentPlace)&&this.getGroup(drag)===this.getGroup(drop)&&this.getChoice(drag)===this.getChoice(oldDrag)&&this.getGroup(drag)===this.getGroup(oldDrag))},DragDropToTextQuestion.prototype.sendDragHome=function(drag){var currentPlace=this.getClassnameNumericSuffix(drag,"inplace");null!==currentPlace&&drag.removeClass("inplace"+currentPlace),drag.data("unplaced",!0),this.animateTo(drag,this.getDragHome(this.getGroup(drag),this.getChoice(drag)))},DragDropToTextQuestion.prototype.handleKeyPress=function(e){var drop=$(e.target).closest(".drop");if(0===drop.length){var placedDrag=$(e.target),currentPlace=this.getClassnameNumericSuffix(placedDrag,"inplace");null!==currentPlace&&(drop=this.getDrop(placedDrag,currentPlace))}var currentDrag=this.getCurrentDragInPlace(this.getPlace(drop)),nextDrag=$();switch(e.keyCode){case keys.space:case keys.arrowRight:case keys.arrowDown:nextDrag=this.getNextDrag(this.getGroup(drop),currentDrag);break;case keys.arrowLeft:case keys.arrowUp:nextDrag=this.getPreviousDrag(this.getGroup(drop),currentDrag);break;case keys.escape:break;default:return void(questionManager.isKeyboardNavigation=!1)}if(nextDrag.length){nextDrag.data("isfocus",!0),nextDrag.addClass("beingdragged");var hiddenDrag=this.getDragClone(nextDrag);if(hiddenDrag.length)if(nextDrag.hasClass("infinite")){var noOfDrags=this.noOfDropsInGroup(this.getGroup(nextDrag));if(this.getInfiniteDragClones(nextDrag,!1).length<noOfDrags){var cloneDrag=nextDrag.clone();cloneDrag.removeClass("beingdragged"),cloneDrag.removeAttr("tabindex"),hiddenDrag.after(cloneDrag),questionManager.addEventHandlersToDrag(cloneDrag),nextDrag.offset(cloneDrag.offset())}else hiddenDrag.addClass("active"),nextDrag.offset(hiddenDrag.offset())}else hiddenDrag.addClass("active"),nextDrag.offset(hiddenDrag.offset())}else drop.data("isfocus",!0);e.preventDefault(),this.sendDragToDrop(nextDrag,drop)},DragDropToTextQuestion.prototype.getNextDrag=function(group,drag){var choice,numChoices=this.noOfChoicesInGroup(group);choice=0===drag.length?1:this.getChoice(drag)+1;for(var next=this.getUnplacedChoice(group,choice);0===next.length&&choice<numChoices;)choice++,next=this.getUnplacedChoice(group,choice);return next},DragDropToTextQuestion.prototype.getPreviousDrag=function(group,drag){var choice;choice=0===drag.length?this.noOfChoicesInGroup(group):this.getChoice(drag)-1;for(var previous=this.getUnplacedChoice(group,choice);0===previous.length&&choice>1;)choice--,previous=this.getUnplacedChoice(group,choice);return previous},DragDropToTextQuestion.prototype.animateTo=function(drag,target){var currentPos=drag.offset(),targetPos=target.offset(),thisQ=this;M.util.js_pending("qtype_ddwtos-animate-"+thisQ.containerId),drag.animate({left:parseInt(drag.css("left"))+targetPos.left-currentPos.left,top:parseInt(drag.css("top"))+targetPos.top-currentPos.top},{duration:"fast",done:function(){$("body").trigger("qtype_ddwtos-dragmoved",[drag,target,thisQ]),M.util.js_complete("qtype_ddwtos-animate-"+thisQ.containerId)}})},DragDropToTextQuestion.prototype.isPointInDrop=function(pageX,pageY,drop){var position=drop.offset();return pageX>=position.left&&pageX<position.left+drop.width()&&pageY>=position.top&&pageY<position.top+drop.height()},DragDropToTextQuestion.prototype.setInputValue=function(place,choice){this.getRoot().find("input.placeinput.place"+place).val(choice)},DragDropToTextQuestion.prototype.getRoot=function(){return $(document.getElementById(this.containerId))},DragDropToTextQuestion.prototype.getDragHome=function(group,choice){return this.getRoot().find(".draghome.dragplaceholder.group"+group+".choice"+choice).is(":visible")?this.getRoot().find(".draghome.dragplaceholder.group"+group+".choice"+choice):this.getRoot().find(".draggrouphomes"+group+" span.draghome.infinite.choice"+choice+".group"+group)},DragDropToTextQuestion.prototype.getUnplacedChoice=function(group,choice){return this.getRoot().find(".draghome.group"+group+".choice"+choice+".unplaced").slice(0,1)},DragDropToTextQuestion.prototype.getCurrentDragInPlace=function(place){return this.getRoot().find("span.draghome.inplace"+place)},DragDropToTextQuestion.prototype.noOfDropsInGroup=function(group){return this.getRoot().find(".drop.group"+group).length},DragDropToTextQuestion.prototype.noOfChoicesInGroup=function(group){return this.getRoot().find(".draghome.group"+group).length},DragDropToTextQuestion.prototype.getClassnameNumericSuffix=function(node,prefix){var classes=node.attr("class");if(void 0!==classes&&""!==classes)for(var classesArr=classes.split(" "),index=0;index<classesArr.length;index++){if(new RegExp("^"+prefix+"([0-9])+$").test(classesArr[index])){var match=new RegExp("([0-9])+$").exec(classesArr[index]);return Number(match[0])}}return null},DragDropToTextQuestion.prototype.getChoice=function(drag){return this.getClassnameNumericSuffix(drag,"choice")},DragDropToTextQuestion.prototype.getGroup=function(node){return this.getClassnameNumericSuffix(node,"group")},DragDropToTextQuestion.prototype.getPlace=function(node){return this.getClassnameNumericSuffix(node,"place")},DragDropToTextQuestion.prototype.getDragClone=function(drag){return this.getRoot().find(".draggrouphomes"+this.getGroup(drag)+" span.draghome.choice"+this.getChoice(drag)+".group"+this.getGroup(drag)+".dragplaceholder")},DragDropToTextQuestion.prototype.getInfiniteDragClones=function(drag,inHome){return inHome?this.getRoot().find(".draggrouphomes"+this.getGroup(drag)+" span.draghome.choice"+this.getChoice(drag)+".group"+this.getGroup(drag)+".infinite").not(".dragplaceholder"):this.getRoot().find("span.draghome.choice"+this.getChoice(drag)+".group"+this.getGroup(drag)+".infinite").not(".dragplaceholder")},DragDropToTextQuestion.prototype.getDrop=function(drag,currentPlace){return this.getRoot().find(".drop.group"+this.getGroup(drag)+".place"+currentPlace)};var questionManager={eventHandlersInitialised:!1,dragEventHandlersInitialised:{},isKeyboardNavigation:!1,questions:{},init:function(containerId,readOnly){if(questionManager.questions[containerId]=new DragDropToTextQuestion(containerId,readOnly),questionManager.eventHandlersInitialised||(questionManager.setupEventHandlers(),questionManager.eventHandlersInitialised=!0),!questionManager.dragEventHandlersInitialised.hasOwnProperty(containerId)){questionManager.dragEventHandlersInitialised[containerId]=!0;var questionContainer=document.getElementById(containerId);questionContainer.classList.contains("ddwtos")&&!questionContainer.classList.contains("qtype_ddwtos-readonly")&&questionManager.addEventHandlersToDrag($(questionContainer).find("span.draghome"))}},setupEventHandlers:function(){$("body").on("keydown",".que.ddwtos:not(.qtype_ddwtos-readonly) span.drop",questionManager.handleKeyPress).on("keydown",".que.ddwtos:not(.qtype_ddwtos-readonly) span.draghome.placed:not(.beingdragged)",questionManager.handleKeyPress).on("qtype_ddwtos-dragmoved",questionManager.handleDragMoved)},addEventHandlersToDrag:function(element){element.unbind("mousedown touchstart"),element.on("mousedown touchstart",questionManager.handleDragStart)},handleDragStart:function(e){e.preventDefault();var question=questionManager.getQuestionForEvent(e);question&&question.handleDragStart(e)},handleKeyPress:function(e){if(!questionManager.isKeyboardNavigation){questionManager.isKeyboardNavigation=!0;var question=questionManager.getQuestionForEvent(e);question&&question.handleKeyPress(e)}},getQuestionForEvent:function(e){var containerId=$(e.currentTarget).closest(".que.ddwtos").attr("id");return questionManager.questions[containerId]},handleDragMoved:function(e,drag,target,thisQ){drag.removeClass("beingdragged"),drag.css("top","").css("left",""),target.after(drag),target.removeClass("active"),void 0!==drag.data("unplaced")&&!0===drag.data("unplaced")&&(drag.removeClass("placed").addClass("unplaced"),drag.removeAttr("tabindex"),drag.removeData("unplaced"),drag.hasClass("infinite")&&thisQ.getInfiniteDragClones(drag,!0).length>1&&thisQ.getInfiniteDragClones(drag,!0).first().remove()),void 0!==drag.data("isfocus")&&!0===drag.data("isfocus")&&(drag.focus(),drag.removeData("isfocus")),void 0!==target.data("isfocus")&&!0===target.data("isfocus")&&target.removeData("isfocus"),questionManager.isKeyboardNavigation&&(questionManager.isKeyboardNavigation=!1),thisQ.isQuestionInteracted()&&(questionManager.handleFormDirty(),thisQ.questionAnswer=thisQ.getQuestionAnsweredValues())},handleFormDirty:function(){const responseForm=document.getElementById("responseform");FormChangeChecker.markFormAsDirty(responseForm)}};return{init:questionManager.init}}));

//# sourceMappingURL=ddwtos.min.js.map