<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Lang file for ddwtos.
 *
 * @package    qtype_ddwtos
 * @copyright  2011 The Open University
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['addmorechoiceblanks'] = 'Blanks for {no} more choices';
$string['answer'] = 'Answer';
$string['blank'] = 'blank';
$string['blanknumber'] = 'Blank {$a}';
$string['correctansweris'] = 'The correct answer is: {$a}';
$string['choicesacceptedtext'] = 'Write the answers to be dragged into the gaps. You can include extra answers to increase difficulty. <br/>
Accepted text formatting: &lt;sub&gt;, &lt;sup&gt;, &lt;b&gt;, &lt;i&gt;, &lt;em&gt;, &lt;strong&gt;. TeX is also accepted, using $$ at the start and at the end.';
$string['errorlimitedchoice'] = 'Choice [[{$a}]] has been used more than once without being set to "Unlimited". Please recheck this question.';
$string['infinite'] = 'Unlimited';
$string['pleaseputananswerineachbox'] = 'Please put an answer in each box.';
$string['pluginname'] = 'Drag and drop into text';
$string['pluginname_help'] = 'Drag and drop into text questions require the respondent to drag words or phrases into the correct gaps in the question text. [[1]], [[2]], [[3]], ... are used as placeholders in the question text, with the correct answers specified as choice answers 1, 2, 3, ... respectively. Choice answers may be grouped so that all answers in a particular group are coloured the same as the gaps for them in the question text. Choice answers marked as unlimited may be used in more than one gap.';
$string['pluginname_link'] = 'question/type/ddwtos';
$string['pluginnameadding'] = 'Adding a drag and drop into text';
$string['pluginnameediting'] = 'Editing a drag and drop into text';
$string['pluginnamesummary'] = 'Missing words in the question text are filled in using drag and drop.';
$string['privacy:metadata'] = 'Drag and drop into text question type plugin allows question authors to set default options as user preferences.';
$string['privacy:preference:defaultmark'] = 'The default mark set for a given question.';
$string['privacy:preference:penalty'] = 'The penalty for each incorrect try when questions are run using the \'Interactive with multiple tries\' or \'Adaptive mode\' behaviour.';
$string['privacy:preference:shuffleanswers'] = 'Whether the answers should be automatically shuffled.';
