/**
 * Question class for drag and drop marker question type, used to support the question and preview pages.
 *
 * @module     qtype_ddmarker/question
 * @copyright  2018 The Open University
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("qtype_ddmarker/question",["jquery","core/dragdrop","qtype_ddmarker/shapes","core/key_codes","core_form/changechecker","core_filters/events"],(function($,dragDrop,Shapes,keys,FormChangeChecker,filterEvent){function DragDropMarkersQuestion(containerId,readOnly,visibleDropZones){var thisQ=this;this.containerId=containerId,this.visibleDropZones=visibleDropZones,this.shapes=[],this.shapeSVGs=[],this.isPrinting=!1,this.questionAnswer={},readOnly&&this.getRoot().addClass("qtype_ddmarker-readonly"),thisQ.allImagesLoaded=!1,thisQ.getNotYetLoadedImages().one("load",(function(){thisQ.waitForAllImagesToBeLoaded()})),thisQ.waitForAllImagesToBeLoaded()}DragDropMarkersQuestion.prototype.drawDropzones=function(){if(this.visibleDropZones.length>0){var bgImage=this.bgImage();this.getRoot().find("div.dropzones").html('<svg xmlns="http://www.w3.org/2000/svg" class="dropzones" width="'+bgImage.outerWidth()+'" height="'+bgImage.outerHeight()+'"></svg>');for(var svg=this.getRoot().find("svg.dropzones"),nextColourIndex=0,dropZoneNo=0;dropZoneNo<this.visibleDropZones.length;dropZoneNo++){var colourClass="color"+nextColourIndex;nextColourIndex=(nextColourIndex+1)%8,this.addDropzone(svg,dropZoneNo,colourClass)}}},DragDropMarkersQuestion.prototype.addDropzone=function(svg,dropZoneNo,colourClass){var existingmarkertext,dropZone=this.visibleDropZones[dropZoneNo],shape=Shapes.make(dropZone.shape,""),bgRatio=this.bgRatio();if(shape.parse(dropZone.coords,bgRatio)){if((existingmarkertext=this.getRoot().find("div.markertexts span.markertext"+dropZoneNo)).length)""!==dropZone.markertext?(existingmarkertext.html(dropZone.markertext),filterEvent.notifyFilterContentUpdated(existingmarkertext)):existingmarkertext.remove();else if(""!==dropZone.markertext){var classnames="markertext markertext"+dropZoneNo;this.getRoot().find("div.markertexts").append('<span class="'+classnames+'">'+dropZone.markertext+"</span>");var markerspan=this.getRoot().find("div.ddarea div.markertexts span.markertext"+dropZoneNo);if(markerspan.length){var handles=shape.getHandlePositions(),positionLeft=handles.moveHandle.x-markerspan.outerWidth()/2-4,positionTop=handles.moveHandle.y-markerspan.outerHeight()/2;markerspan.css("left",positionLeft).css("top",positionTop),markerspan.data("originX",markerspan.position().left/bgRatio).data("originY",markerspan.position().top/bgRatio),this.handleElementScale(markerspan,"center")}filterEvent.notifyFilterContentUpdated(markerspan)}var shapeSVG=shape.makeSvg(svg[0]);shapeSVG.setAttribute("class","dropzone "+colourClass),this.shapes[this.shapes.length]=shape,this.shapeSVGs[this.shapeSVGs.length]=shapeSVG}},DragDropMarkersQuestion.prototype.repositionDrags=function(){var root=this.getRoot(),thisQ=this;root.find("div.draghomes .marker").not(".dragplaceholder").each((function(key,item){$(item).addClass("unneeded")})),root.find("input.choices").each((function(key,input){var choiceNo=thisQ.getChoiceNoFromElement(input),imageCoords=thisQ.getImageCoords(input);if(imageCoords.length){var drag=thisQ.getRoot().find(".draghomes span.marker.choice"+choiceNo).not(".dragplaceholder");drag.remove();for(var i=0;i<imageCoords.length;i++){var dragInDrop=drag.clone();const screenCoords=thisQ.convertToWindowXY(imageCoords[i]);dragInDrop.data("pagex",screenCoords.x).data("pagey",screenCoords.y),dragInDrop.data("imageCoords",imageCoords[i]),dragInDrop.data("scaleRatio",1),thisQ.sendDragToDrop(dragInDrop,!1,!0)}thisQ.getDragClone(drag).addClass("active"),thisQ.cloneDragIfNeeded(drag)}})),thisQ.questionAnswer=thisQ.getQuestionAnsweredValues()},DragDropMarkersQuestion.prototype.getQuestionAnsweredValues=function(){let result={};return this.getRoot().find("input.choices").each(((i,inputNode)=>{result[inputNode.id]=inputNode.value})),result},DragDropMarkersQuestion.prototype.isQuestionInteracted=function(){const oldAnswer=this.questionAnswer,newAnswer=this.getQuestionAnsweredValues();let isInteracted=!1;return JSON.stringify(newAnswer)!==JSON.stringify(oldAnswer)?(isInteracted=!0,isInteracted):(Object.keys(newAnswer).forEach((key=>{newAnswer[key]!==oldAnswer[key]&&(isInteracted=!0)})),isInteracted)},DragDropMarkersQuestion.prototype.getImageCoords=function(inputNode){var imageCoords=[],val=$(inputNode).val();if(""!==val)for(var coordsStrings=val.split(";"),i=0;i<coordsStrings.length;i++)imageCoords[i]=Shapes.Point.parse(coordsStrings[i]);return imageCoords},DragDropMarkersQuestion.prototype.convertToWindowXY=function(point){var bgImage=this.bgImage();return point.offset(bgImage.offset().left+1,bgImage.offset().top+1)},DragDropMarkersQuestion.prototype.convertToBgImgXY=function(point){var bgImage=this.bgImage();return point.offset(-bgImage.offset().left-1,-bgImage.offset().top-1)},DragDropMarkersQuestion.prototype.coordsInBgImg=function(point){var bgImage=this.bgImage(),bgPosition=bgImage.offset();return point.x>=bgPosition.left&&point.x<bgPosition.left+bgImage.width()&&point.y>=bgPosition.top&&point.y<bgPosition.top+bgImage.height()},DragDropMarkersQuestion.prototype.getRoot=function(){return $(document.getElementById(this.containerId))},DragDropMarkersQuestion.prototype.bgImage=function(){return this.getRoot().find("img.dropbackground")},DragDropMarkersQuestion.prototype.handleDragStart=function(e){var thisQ=this,dragged=$(e.target).closest(".marker");if(dragDrop.prepare(e).start){if(dragged.addClass("beingdragged").css("transform",""),!!dragged.hasClass("unneeded")){var hiddenDrag=thisQ.getDragClone(dragged);hiddenDrag.length&&(hiddenDrag.addClass("active"),dragged.offset(hiddenDrag.offset()))}dragDrop.start(e,dragged,(function(){}),(function(x,y,dragged){thisQ.dragEnd(dragged)}))}},DragDropMarkersQuestion.prototype.dragEnd=function(dragged){var dragXY,placed=!1,choiceNo=this.getChoiceNoFromElement(dragged),bgRatio=this.bgRatio();if(dragged.data("pagex",dragged.offset().left).data("pagey",dragged.offset().top),dragXY=new Shapes.Point(dragged.data("pagex"),dragged.data("pagey")),this.coordsInBgImg(dragXY)){this.sendDragToDrop(dragged,!0),placed=!0,dragged.data("imageCoords")&&dragged.data("imageCoords",null);var bgImgXY=this.convertToBgImgXY(dragXY);bgImgXY=new Shapes.Point(bgImgXY.x/bgRatio,bgImgXY.y/bgRatio),dragged.data("originX",bgImgXY.x).data("originY",bgImgXY.y)}placed?this.cloneDragIfNeeded(dragged):(this.sendDragHome(dragged),this.removeDragIfNeeded(dragged)),this.saveCoordsForChoice(choiceNo)},DragDropMarkersQuestion.prototype.saveCoordsForChoice=function(choiceNo){let imageCoords=[];var items=this.getRoot().find("div.droparea span.marker.choice"+choiceNo),thiQ=this,bgRatio=this.bgRatio();items.length&&items.each((function(){var drag=$(this);if(drag.hasClass("beingdragged")||drag.data("imageCoords"))drag.data("imageCoords")&&(imageCoords[imageCoords.length]=drag.data("imageCoords"));else{drag.data("scaleRatio")!==bgRatio&&drag.data("pagex",drag.offset().left).data("pagey",drag.offset().top);var dragXY=new Shapes.Point(drag.data("pagex"),drag.data("pagey"));if(thiQ.coordsInBgImg(dragXY)){var bgImgXY=thiQ.convertToBgImgXY(dragXY);bgImgXY=new Shapes.Point(bgImgXY.x/bgRatio,bgImgXY.y/bgRatio),imageCoords[imageCoords.length]=bgImgXY}}})),this.getRoot().find("input.choice"+choiceNo).val(imageCoords.join(";")),this.isQuestionInteracted()&&(questionManager.handleFormDirty(),this.questionAnswer=this.getQuestionAnsweredValues())},DragDropMarkersQuestion.prototype.handleKeyPress=function(e){var drag=$(e.target).closest(".marker"),point=new Shapes.Point(drag.offset().left,drag.offset().top),choiceNo=this.getChoiceNoFromElement(drag);switch(e.keyCode){case keys.arrowLeft:case 65:point.x-=1;break;case keys.arrowRight:case 68:point.x+=1;break;case keys.arrowDown:case 83:point.y+=1;break;case keys.arrowUp:case 87:point.y-=1;break;case keys.space:case keys.escape:point=null;break;default:return}if(e.preventDefault(),null!==point){point=this.constrainToBgImg(point),drag.offset({left:point.x,top:point.y}),drag.data("pagex",drag.offset().left).data("pagey",drag.offset().top);var dragXY=this.convertToBgImgXY(new Shapes.Point(drag.data("pagex"),drag.data("pagey")));if(drag.data("originX",dragXY.x/this.bgRatio()).data("originY",dragXY.y/this.bgRatio()),this.coordsInBgImg(new Shapes.Point(drag.offset().left,drag.offset().top))&&drag.hasClass("unneeded")){this.sendDragToDrop(drag,!0);var hiddenDrag=this.getDragClone(drag);hiddenDrag.length&&hiddenDrag.addClass("active"),this.cloneDragIfNeeded(drag)}}else drag.css("left","").css("top",""),drag.data("pagex",drag.offset().left).data("pagey",drag.offset().top),this.sendDragHome(drag),this.removeDragIfNeeded(drag);drag.focus(),this.saveCoordsForChoice(choiceNo)},DragDropMarkersQuestion.prototype.constrainToBgImg=function(windowxy){var bgImg=this.bgImage(),bgImgXY=this.convertToBgImgXY(windowxy);return bgImgXY.x=Math.max(0,bgImgXY.x),bgImgXY.y=Math.max(0,bgImgXY.y),bgImgXY.x=Math.min(bgImg.width(),bgImgXY.x),bgImgXY.y=Math.min(bgImg.height(),bgImgXY.y),this.convertToWindowXY(bgImgXY)},DragDropMarkersQuestion.prototype.getChoiceNoFromElement=function(node){return Number(this.getClassnameNumericSuffix(node,"choice"))},DragDropMarkersQuestion.prototype.getClassnameNumericSuffix=function(node,prefix){var classes=$(node).attr("class");if(void 0!==classes&&""!==classes)for(var classesarr=classes.split(" "),index=0;index<classesarr.length;index++){if(new RegExp("^"+prefix+"([0-9])+$").test(classesarr[index])){var match=new RegExp("([0-9])+$").exec(classesarr[index]);return Number(match[0])}}return null},DragDropMarkersQuestion.prototype.handleResize=function(){var thisQ=this,bgRatio=this.bgRatio();this.isPrinting&&(bgRatio=1),this.getRoot().find("div.droparea .marker").not(".beingdragged").each((function(key,drag){$(drag).css("left",parseFloat($(drag).data("originX"))*parseFloat(bgRatio)).css("top",parseFloat($(drag).data("originY"))*parseFloat(bgRatio)),thisQ.handleElementScale(drag,"left top")})),this.getRoot().find("div.droparea svg.dropzones").width(this.bgImage().width()).height(this.bgImage().height());for(var dropZoneNo=0;dropZoneNo<this.visibleDropZones.length;dropZoneNo++){var originCoords=thisQ.visibleDropZones[dropZoneNo].coords,shape=thisQ.shapes[dropZoneNo],shapeSVG=thisQ.shapeSVGs[dropZoneNo];shape.parse(originCoords,bgRatio),shape.updateSvg(shapeSVG);var handles=shape.getHandlePositions(),markerSpan=this.getRoot().find("div.ddarea div.markertexts span.markertext"+dropZoneNo);markerSpan.css("left",handles.moveHandle.x-markerSpan.outerWidth()/2-4).css("top",handles.moveHandle.y-markerSpan.outerHeight()/2),thisQ.handleElementScale(markerSpan,"center")}},DragDropMarkersQuestion.prototype.cloneDrags=function(){var thisQ=this;this.getRoot().find("div.draghomes span.marker").each((function(index,draghome){var drag=$(draghome),placeHolder=drag.clone();placeHolder.removeClass(),placeHolder.addClass("marker"),placeHolder.addClass("choice"+thisQ.getChoiceNoFromElement(drag)),placeHolder.addClass(thisQ.getDragNoClass(drag,!1)),placeHolder.addClass("dragplaceholder"),drag.before(placeHolder)}))},DragDropMarkersQuestion.prototype.getDragNo=function(drag){return this.getClassnameNumericSuffix(drag,"dragno")},DragDropMarkersQuestion.prototype.getDragNoClass=function(drag,includeSelector){var className="dragno"+this.getDragNo(drag);return this.isInfiniteDrag(drag)&&(className="infinite"),includeSelector?"."+className:className},DragDropMarkersQuestion.prototype.getDragClone=function(drag){return this.getRoot().find(".draghomes span.marker.choice"+this.getChoiceNoFromElement(drag)+this.getDragNoClass(drag,!0)+".dragplaceholder")},DragDropMarkersQuestion.prototype.dropArea=function(){return this.getRoot().find("div.droparea")},DragDropMarkersQuestion.prototype.sendDragHome=function(drag){drag.removeClass("beingdragged").addClass("unneeded").css("top","").css("left","").css("transform","");var placeHolder=this.getDragClone(drag);placeHolder.after(drag),placeHolder.removeClass("active")},DragDropMarkersQuestion.prototype.sendDragToDrop=function(drag,isScaling){let initialLoad=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var dropArea=this.dropArea(),bgRatio=this.bgRatio();drag.removeClass("beingdragged").removeClass("unneeded");var dragXY=this.convertToBgImgXY(new Shapes.Point(drag.data("pagex"),drag.data("pagey")));isScaling?(drag.data("originX",dragXY.x/bgRatio).data("originY",dragXY.y/bgRatio),drag.css("left",dragXY.x).css("top",dragXY.y)):(drag.data("originX",dragXY.x).data("originY",dragXY.y),drag.css("left",dragXY.x*bgRatio).css("top",dragXY.y*bgRatio)),initialLoad||drag.data("scaleRatio",bgRatio),dropArea.append(drag),this.handleElementScale(drag,"left top")},DragDropMarkersQuestion.prototype.cloneDragIfNeeded=function(drag){var inputNode=this.getInput(drag),noOfDrags=Number(this.getClassnameNumericSuffix(inputNode,"noofdrags")),displayedDragsInDropArea=this.getRoot().find("div.droparea .marker.choice"+this.getChoiceNoFromElement(drag)+this.getDragNoClass(drag,!0)).length,displayedDragsInDragHomes=this.getRoot().find("div.draghomes .marker.choice"+this.getChoiceNoFromElement(drag)+this.getDragNoClass(drag,!0)).not(".dragplaceholder").length;if((this.isInfiniteDrag(drag)||!this.isInfiniteDrag(drag)&&displayedDragsInDropArea<noOfDrags)&&0===displayedDragsInDragHomes){var dragClone=drag.clone();dragClone.addClass("unneeded").css("top","").css("left","").css("transform",""),this.getDragClone(drag).removeClass("active").after(dragClone),questionManager.addEventHandlersToMarker(dragClone)}},DragDropMarkersQuestion.prototype.removeDragIfNeeded=function(drag){for(var dragsInHome=this.getRoot().find("div.draghomes .marker.choice"+this.getChoiceNoFromElement(drag)+this.getDragNoClass(drag,!0)).not(".dragplaceholder"),displayedDrags=dragsInHome.length;displayedDrags>1;)dragsInHome.first().remove(),displayedDrags--},DragDropMarkersQuestion.prototype.getInput=function(drag){var choiceNo=this.getChoiceNoFromElement(drag);return this.getRoot().find("input.choices.choice"+choiceNo)},DragDropMarkersQuestion.prototype.bgRatio=function(){var bgImg=this.bgImage(),bgImgNaturalWidth=bgImg.get(0).naturalWidth;return bgImg.width()/bgImgNaturalWidth},DragDropMarkersQuestion.prototype.handleElementScale=function(element,type){var bgRatio=parseFloat(this.bgRatio());this.isPrinting&&(bgRatio=1),$(element).css({"-webkit-transform":"scale("+bgRatio+")","-moz-transform":"scale("+bgRatio+")","-ms-transform":"scale("+bgRatio+")","-o-transform":"scale("+bgRatio+")",transform:"scale("+bgRatio+")","transform-origin":type})},DragDropMarkersQuestion.prototype.isInfiniteDrag=function(drag){return drag.hasClass("infinite")},DragDropMarkersQuestion.prototype.waitForAllImagesToBeLoaded=function(){var thisQ=this;this.allImagesLoaded||(null!==this.imageLoadingTimeoutId&&clearTimeout(this.imageLoadingTimeoutId),this.getNotYetLoadedImages().length>0?this.imageLoadingTimeoutId=setTimeout((function(){this.waitForAllImagesToBeLoaded()}),100):(this.allImagesLoaded=!0,this.cloneDrags(),this.repositionDrags(),this.drawDropzones(),document.addEventListener(filterEvent.eventTypes.filterContentRenderingComplete,(elements=>{elements.detail.nodes.forEach((element=>{thisQ.changeAllMakerToFilteredContent(element)}))}))))},DragDropMarkersQuestion.prototype.changeAllMakerToFilteredContent=function(filteredElement){let currentFilteredItem=$(filteredElement);const parentIsMarker=currentFilteredItem.parent().closest("span.marker"),isMarker=currentFilteredItem.hasClass("marker"),root=this.getRoot();if(!parentIsMarker&&!isMarker)return;if(parentIsMarker&&(currentFilteredItem=currentFilteredItem.parent().closest("span.marker")),root.find(currentFilteredItem).length<=0)return;const dragNo=this.getDragNo(currentFilteredItem),choiceNo=this.getChoiceNoFromElement(currentFilteredItem),listOfContainerToBeModifed=["div.draghomes .marker:not(.dragplaceholder).dragno"+dragNo+".choice"+choiceNo,"div.droparea .marker:not(.dragplaceholder).dragno"+dragNo+".choice"+choiceNo,"div.draghomes .marker:not(.dragplaceholder).infinite.choice"+choiceNo,"div.droparea .marker:not(.dragplaceholder).infinite.choice"+choiceNo];let listOfModifiedDragDrop=[];const filteredDragDropClone=currentFilteredItem.clone();listOfContainerToBeModifed.forEach((function(selector){root.find(selector).each((function(i,node){const originalClass=$(node).attr("class"),originalStyle=$(node).attr("style");filteredDragDropClone.attr("class",originalClass),filteredDragDropClone.attr("style",originalStyle),questionManager.addEventHandlersToMarker(filteredDragDropClone),$(node).before(filteredDragDropClone),listOfModifiedDragDrop.push(node)}))})),listOfModifiedDragDrop.forEach((function(node){$(node).remove()}))},DragDropMarkersQuestion.prototype.getNotYetLoadedImages=function(){return this.getRoot().find(".ddmarker img.dropbackground").not((function(i,imgNode){return this.imageIsLoaded(imgNode)}))},DragDropMarkersQuestion.prototype.imageIsLoaded=function(imgElement){return imgElement.complete&&0!==imgElement.naturalHeight};var questionManager={eventHandlersInitialised:!1,markerEventHandlersInitialised:{},isPrinting:!1,isKeyboardNavigation:!1,questions:{},init:function(containerId,readOnly,visibleDropZones){if(questionManager.questions[containerId]=new DragDropMarkersQuestion(containerId,readOnly,visibleDropZones),questionManager.eventHandlersInitialised||(questionManager.setupEventHandlers(),questionManager.eventHandlersInitialised=!0),!questionManager.markerEventHandlersInitialised.hasOwnProperty(containerId)){questionManager.markerEventHandlersInitialised[containerId]=!0;var questionContainer=document.getElementById(containerId);questionContainer.classList.contains("ddmarker")&&!questionContainer.classList.contains("qtype_ddmarker-readonly")&&(questionManager.addEventHandlersToMarker($(questionContainer).find("div.draghomes .marker")),questionManager.addEventHandlersToMarker($(questionContainer).find("div.droparea .marker")))}},setupEventHandlers:function(){$(window).on("resize",(function(){questionManager.handleWindowResize(!1)})),window.addEventListener("beforeprint",(function(){questionManager.isPrinting=!0,questionManager.handleWindowResize(questionManager.isPrinting)})),window.addEventListener("afterprint",(function(){questionManager.isPrinting=!1,questionManager.handleWindowResize(questionManager.isPrinting)})),setTimeout((function(){questionManager.fixLayoutIfThingsMoved()}),100)},addEventHandlersToMarker:function(element){element.on("mousedown touchstart",questionManager.handleDragStart).on("keydown keypress",questionManager.handleKeyPress).focusin((function(e){questionManager.handleKeyboardFocus(e,!0)})).focusout((function(e){questionManager.handleKeyboardFocus(e,!1)}))},handleDragStart:function(e){e.preventDefault();var question=questionManager.getQuestionForEvent(e);question&&question.handleDragStart(e)},handleKeyPress:function(e){var question=questionManager.getQuestionForEvent(e);question&&question.handleKeyPress(e)},handleWindowResize:function(isPrinting){for(var containerId in questionManager.questions)questionManager.questions.hasOwnProperty(containerId)&&(questionManager.questions[containerId].isPrinting=isPrinting,questionManager.questions[containerId].handleResize())},handleKeyboardFocus:function(e,isNavigating){questionManager.isKeyboardNavigation=isNavigating},fixLayoutIfThingsMoved:function(){questionManager.isKeyboardNavigation||this.handleWindowResize(questionManager.isPrinting),setTimeout((function(){questionManager.fixLayoutIfThingsMoved(questionManager.isPrinting)}),100)},getQuestionForEvent:function(e){var containerId=$(e.currentTarget).closest(".que.ddmarker").attr("id");return questionManager.questions[containerId]},handleFormDirty:function(){const responseForm=document.getElementById("responseform");FormChangeChecker.markFormAsDirty(responseForm)}};return{init:questionManager.init}}));

//# sourceMappingURL=question.min.js.map