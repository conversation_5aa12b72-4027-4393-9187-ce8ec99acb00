@core @core_question @javascript
Feature: A teacher can edit questions in the question bank
  In order to improve my questions
  As a teacher
  I need to be able to edit questions

  Background:
    Given the following "users" exist:
      | username | firstname | lastname | email |
      | teacher1 | Teacher | 1 | <EMAIL> |
    And the following "courses" exist:
      | fullname | shortname | format |
      | Course 1 | C1 | weeks |
    And the following "course enrolments" exist:
      | user | course | role |
      | teacher1 | C1 | editingteacher |
    And the following "activities" exist:
      | activity   | name    | intro              | course | idnumber |
      | qbank      | Qbank 1 | Question bank 1    | C1     | qbank1   |
    And the following "question categories" exist:
      | contextlevel    | reference | name           |
      | Activity module | qbank1    | Test questions |
    And the following "questions" exist:
      | questioncategory | qtype | name                       | questiontext                  |
      | Test questions   | essay | Test question to be edited | Write about whatever you want |
    And I am on the "Qbank 1" "core_question > question bank" page logged in as "teacher1"

  @javascript
  Scenario: Edit a previously created question
    When I am on the "Test question to be edited" "core_question > edit" page logged in as "teacher1"
    And I set the following fields to these values:
      | Question name | Edited question name |
      | Question text | Write a lot about what you want |
    And I press "id_submitbutton"
    Then I should see "Edited question name"
    And I should not see "Test question to be edited"
    And I should see "Teacher 1"

  Scenario: Edit a previously created question without permission 'moodle/question:moveall' and 'moodle/question:movemine'
    Given I log in as "admin"
    And the following "permission overrides" exist:
      | capability               | permission | role           | contextlevel | reference |
      | moodle/question:movemine | Prevent    | editingteacher | System       |           |
      | moodle/question:moveall  | Prevent    | editingteacher | System       |           |
    When I am on the "Test question to be edited" "core_question > edit" page logged in as "teacher1"
    And I set the following fields to these values:
      | Question name | Edited question name            |
      | Question text | Write a lot about what you want |
    And I press "id_submitbutton"
    Then I should see "Edited question name"
    And I should not see "Test question to be edited"
    And I should see "Teacher 1"

  Scenario: Edit a previously created question without permission 'moodle/question:editall' and 'moodle/question:editmine'
    Given I log in as "admin"
    And the following "permission overrides" exist:
      | capability               | permission | role           | contextlevel | reference |
      | moodle/question:editmine | Prevent    | editingteacher | System       |           |
      | moodle/question:editall  | Prevent    | editingteacher | System       |           |
    When I am on the "Test question to be edited" "core_question > edit" page logged in as "teacher1"
    And I set the following fields to these values:
      | Question name | Edited question name            |
      | Question text | Write a lot about what you want |
    And I press "id_submitbutton"
    Then I should see "You don't have permission to edit questions from here."

  Scenario: Editing a question can be cancelled
    When I am on the "Test question to be edited" "core_question > edit" page logged in as "teacher1"
    And I set the field "Question name" to "Edited question name"
    And I press "Cancel"
    Then I should see "Test question to be edited"
    And I should see "Admin User"

  Scenario: A question can have its idnumber removed
    Given the following "questions" exist:
      | questioncategory | qtype | name                   | idnumber |
      | Test questions   | essay | Question with idnumber | frog     |
    When I am on the "Qbank 1" "core_question > question bank" page logged in as "teacher1"
    Then I should see "frog" in the "Question with idnumber" "table_row"
    When I choose "Edit question" action for "Question with idnumber" in the question bank
    And I set the field "ID number" to ""
    And I press "id_submitbutton"
    Then I should not see "frog" in the "Question with idnumber" "table_row"

  Scenario: If the question type is no longer installed, then most edit actions are not present
    Given the following "questions" exist:
      | questioncategory | qtype       | name            | questiontext    |
      | Test questions   | missingtype | Broken question | Write something |
    When I am on the "Qbank 1" "core_question > question bank" page logged in as "teacher1"
    Then the "Edit question" item should not exist in the "Edit" action menu of the "Broken question" "table_row"
    And the "Duplicate" item should not exist in the "Edit" action menu of the "Broken question" "table_row"
    And the "Preview" item should not exist in the "Edit" action menu of the "Broken question" "table_row"
    And the "Export as XML" item should not exist in the "Edit" action menu of the "Broken question" "table_row"
    And the "Manage tags" item should exist in the "Edit" action menu of the "Broken question" "table_row"
    And the "Delete" item should exist in the "Edit" action menu of the "Broken question" "table_row"
