<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace core_question;

use core_question\local\bank\question_edit_contexts;
use core_question\local\bank\view;
use testable_core_question_column;

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot . '/question/editlib.php');
require_once($CFG->dirroot . '/question/tests/fixtures/testable_core_question_column.php');

/**
 * Unit tests for the question bank column class.
 *
 * @package core_question
 * @copyright 2018 <PERSON><PERSON> <<EMAIL>>
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class question_bank_column_test extends \advanced_testcase {

    /**
     * Test function display_header multiple sorts with no custom tooltips.
     *
     */
    public function test_column_header_multi_sort_no_tooltips(): void {
        $this->resetAfterTest();
        $course = $this->getDataGenerator()->create_course();
        $qbank = $this->getDataGenerator()->create_module('qbank', ['course' => $course->id]);
        $cm = get_coursemodule_from_id('qbank', $qbank->cmid);
        $questionbank = new view(
                new question_edit_contexts(\context_module::instance($cm->id)),
                new \moodle_url('/'),
                $course,
                $cm
        );
        $columnbase = new testable_core_question_column($questionbank);

        $sortable = [
                'apple' => [
                        'field' => 'apple',
                        'title' => 'Apple'
                ],
                'banana' => [
                        'field' => 'banana',
                        'title' => 'Banana'
                ]
        ];
        $columnbase->set_sortable($sortable);

        ob_start();
        $columnbase->display_header();
        $output = ob_get_clean();

        $this->assertStringContainsString(' title="Sort by Apple ascending">', $output);
        $this->assertStringContainsString(' title="Sort by Banana ascending">', $output);
    }

    /**
     * Test function display_header multiple sorts with custom tooltips.
     *
     */
    public function test_column_header_multi_sort_with_tooltips(): void {
        $this->resetAfterTest();
        $course = $this->getDataGenerator()->create_course();
        $qbank = $this->getDataGenerator()->create_module('qbank', ['course' => $course->id]);
        $cm = get_coursemodule_from_id('qbank', $qbank->cmid);
        $questionbank = new view(
                new question_edit_contexts(\context_module::instance($cm->id)),
                new \moodle_url('/'),
                $course,
                $cm
        );
        $columnbase = new testable_core_question_column($questionbank);

        $sortable = [
                'apple' => [
                        'field' => 'apple',
                        'title' => 'Apple',
                        'tip' => 'Apple Tooltips'
                ],
                'banana' => [
                        'field' => 'banana',
                        'title' => 'Banana',
                        'tip' => 'Banana Tooltips'
                ]
        ];
        $columnbase->set_sortable($sortable);

        ob_start();
        $columnbase->display_header();
        $output = ob_get_clean();

        $this->assertStringContainsString(' title="Sort by Apple Tooltips ascending">', $output);
        $this->assertStringContainsString(' title="Sort by Banana Tooltips ascending">', $output);
    }
}
