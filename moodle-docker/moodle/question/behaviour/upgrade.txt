=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes for question behaviour plugins.

=== 4.0 ===

1) The major question bank changes should not affect behaviour plugins.
   The navigation changes may affect Behat tests. If you encounter this,
   the best way to fix it is to use the new navigation steps in MDL-74130.


=== 3.10 ===

1) The slot parameter of method M.core_question_engine.init_submit_button now removed.
   The method will get the unique id by using the 'Check' button element.


=== 3.1 ===

1) The standard behaviours that use a 'Check' button have all been changed so
   that they only show the button when the question is active. Your behaviour
   may interit this behaviour, because the change was made in the base class,
   and this is probably good for consistency. However, if your question behaviour
   uses the Check button, your probably want to test it carefully, and you will
   probably have to update your unit tests. See MDL-53304 for more details.


=== 2.9 ===

1) There are new methods question_behaviour::can_finish_during_attempt and
   question_behaviour_type::can_finish_during_attempt. These methods both return
   false by default. You should override it if, with your behaviour, questions may
   finish just through the student interacting with them (e.g. by clicking the
   Check button within the question).

   The behaviour type method answers the the question for this behaviour in
   general, without reference to a specific question. The method on the
   behaviour class answers the question for a specific attempt at a specific
   question.


=== 2.7 ===

1) question_behaviour_type has a new method allows_multiple_submitted_responses
      which defaults to false but should return true if this question behaviour
      accepts multiple submissions of responses within one attempt eg. multiple
      tries for the interactive or adaptive question behaviours.
   question_behaviour has a new method step_has_a_submitted_response($step). For
      question behaviours where it is not only the final response that is
      submitted by the student, you need to override this method to return true
      for other steps where a student has submitted a response. See
      question_behaviour_with_multiple_tries::step_has_a_submitted_response($step)
      for example. This method only needs to be overriden if you are returning
      true from allows_multiple_response_submissions.


=== 2.6 ===

1) Legacy required_question_definition_type no longer supported. (See 2.2 point 2) below.)

2) Behaviours now have to define an extra class
class qbehaviour_mybehaviour_type extends question_behaviour_type {
   This class returns information about the type of behaviour, as opposed to
   the qbehaviour_mybehaviour class which controls a particular
   question_attempt. That is like the difference between the qtype_mytype and
   the qtype_mytype_question classes.

   Practically, what this means is that any of the methods that used to be
   static methods of qbehaviour_mybehaviour class are now normal instance
   methods of the qbehaviour_mybehaviour_type class. Specifically.
    2.5 / qbehaviour_mybehaviour -> 2.6 / qbehaviour_mybehaviour_type
    IS_ARCHETYPAL                -> is_archetypal()
    adjust_random_guess_score()  -> adjust_random_guess_score()
    get_unused_display_options() -> get_unused_display_options()

3) The static method is_manual_grade_in_range has moved from the
   question_behaviour class to the question_engine class.

4) Behaviours can now control how the marks information is displayed in the
   grey info area to the left of the question. There is a new method
   mark_summary that you can override, although the default implementation is
   fine in most cases. it uses the marked_out_of_max and mark_out_of_max methods
   as appropriate, so you may just wish to override those.


=== 2.3 ===

1) This plugin type now supports cron in the standard way. If required, Create a
   lib.php file containing
function qbehaviour_mypluginname_cron() {};


=== 2.2 ===

1) The old
    public static function get_required_behaviours()
   method is no more. Instead use the ->dependencies facility in version.php. E.g.
$plugin->dependencies = array(
    'qbehaviour_immediatefeedback' => 2011102700,
    'qbehaviour_deferredcbm'       => 2011102700
);

2) The old required_question_definition_type method has been replaced by a new
   is_compatible_question method. You should change your behaviour to override
   the new method, not the old one. This change has been implemented in a
   backwards-compatible way, so behaviours will not break.
