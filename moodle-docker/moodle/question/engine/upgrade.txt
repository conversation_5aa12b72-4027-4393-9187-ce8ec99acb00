=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes for the core question engine.

=== 4.4 ===

* The method question_usage_by_activity::add_question_in_place_of_other has been made more flexible.
  There is a new argument $keepoldquestionattempt. That defaults to true, which behaves the same as
  the old API, but if you pass false, then the newly added question_attempt completely replaces the
  existing one in-place.

=== 4.2 ===

* A `$questionidentifier` property has been added to `\question_display_options` to enable question type plugins to associate the
question number to the question that is being rendered. This can be used to improve the accessibility of rendered
questions and can be especially be helpful for screen reader users as adding the question number on the answer field(s) labels
will allow them to distinguish between answer fields as they navigate through a quiz.
* Question type plugins can use \question_display_options::add_question_identifier_to_label() to add the question number to the
label of the answer field(s) of the question that is being rendered. The question number may be redundant when displayed, so the
function allows for it to be enclosed within an sr-only container.

=== 4.0 ===
1) A new optional parameter $extraselect has been added as a part of load_questions_usages_where_question_in_state()
   method in question/engine/datalib.php, anything passed here will be added to the SELECT list, use this to return extra data.

=== 3.9 ===

1) In the past, whenever a question_usage_by_activity was loaded from the database,
   the apply_attempt_state was immediately called on every question, whether the
   results of doing that were ever used, or not.

   Now we have changed the code flow, so that apply_attempt_state is only called
   when some data or processing is requested (e.g. analysing a response or rendering
   the question) which requires the question to be fully initialised. This is MDL-67183.

   This change should be completely invisible with everything handled by the question
   engine. If you don't change your code, it should continue to work.

   However, to get the full advantage of this change, you should review your code,
   and look at every call to get_question or get_behaviour (on a question_attempt or
   question_usage_by_activity). The problem with these methods is that the question engine
   cannot know what you are planning to do with the question once you have got it.
   Therefore, they have to assume that apply_attempt_state must be called - which can be expensive.
   If you know that you don't need that (because, for example, you are just going to
   look at ->id or ->questiontext or something simple) then you should pass
   false to these functions, to get the possible performance benefit.
   In addition, there is a new method $qa->get_question_id() to handle that case more simply.

   Note that you don't have worry about this in places like the renderer for your question
   type, because by the time you are in the renderer, the question will already have been
   initialised.


=== 3.7 ===

1) When a question is rendered, the outer div of the question has an id="q123"
   added. Unfortunately, this id was not actually unique, leading to bugs like
   MDL-52572. Therefore, we have had to change it. The id used now is what
   is returned by the new method $qa->get_outer_question_div_unique_id().
   The old code that you need to search for and replace with a call to this
   method is "'q' . $qa->get_slot()"

   Note, the new method has also been added to Moodle 3.5.6 and 3.6.4, but
   returning the old id. This is to help question types that want to support
   multiple Moodle versions.

=== 3.1, 3.0.3, 2.9.5 ===

1) The field question_display_options::$extrainfocontent is now displayed in the
   outcomes (yellow) div by default. It used to be in the info div. If you have
   overriden the question renderer, you may need to make a corresponding change.


=== 3.0, 2.9.2, 2.8.8 ===

1) The extra internal PARAM constant question_attempt::PARAM_MARK should no
   longer be used. (It should not have been used outside the core of the
   question system). See MDL-51090 if you want more explanation.


=== 2.9 ===

1) Some new methods on the question_usage class (and corresponding methods on
   question_attempt, question_attempt_step, question_usage_observer, ... requried
   to implement them, but almost certainly you should only be calling the
   question_usage methods from your code.

   * question_usage::add_question_in_place_of_other($slot, $question, $maxmark = null)

     This creates a new questoin_attempt in place of an existing one, moving the
     existing question_attempt to the end of the usage, in a new slot number.
     The new slot number is returned. The goal is to replace the old attempt, but
     not lose the old data.

   * question_usage::set_question_max_mark($slot, $maxmark)

     Sets the max mark for one question in this usage. Previously, you could
     only change this using the bulk operation question_usage::set_max_mark_in_attempts;

   * question_usage::set_question_attempt_metadata($slot, $name, $value);
     question_usage::get_question_attempt_metadata($slot, $name);

     You can now record metadata, that is, values stored by name, against
     question_attempts. The question engine ignores this data (other than storing
     and loading it) but you may find it useful in your code.

   To see examples of where these are used, look at the chagnes from MDL-40992.

2) New fields in question_display_options, ->extrainfocontent and ->extrahistorycontent.
   These default to blank, but can be used to inject extra content into those parts
   of the question display. If you have overridden the methods in
   core_question_renderer that use these fields, you may need to update your renderer.


=== 2.6 ===

1) The method question_behaviour::is_manual_grade_in_range and move and become
   question_engine::is_manual_grade_in_range.

2) The arguments to core_question_renderer::mark_summary changed from
   ($qa, $options) to ($qa, $behaviouroutput, $options). If you have overridden
   that method you will need to update your code.

3) Heading level for number(), add_part_heading() and respond_history()
   has been lowered by one level. These changes are part of improving the page
   accessibility and making heading to have proper nesting. (MDL-41615)

=== Earlier changes ===

* Were not documented in this way. Sorry.
