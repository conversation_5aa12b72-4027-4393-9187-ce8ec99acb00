define("qbank_viewquestiontype/datafilter/filtertype/type",["exports","core/datafilter/filtertype"],(function(_exports,_filtertype){var obj;
/**
   * Filter managing hidden questions.
   *
   * @module     qbank_viewquestiontype/datafilter/filtertypes/type
   * <AUTHOR> <<EMAIL>>
   * @copyright  2024 Catalyst IT Europe Ltd
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_filtertype=(obj=_filtertype)&&obj.__esModule?obj:{default:obj};class _default extends _filtertype.default{get values(){return this.rawValues}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=type.min.js.map