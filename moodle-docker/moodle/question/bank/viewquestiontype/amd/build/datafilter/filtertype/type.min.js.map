{"version": 3, "file": "type.min.js", "sources": ["../../../src/datafilter/filtertype/type.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Filter managing hidden questions.\n *\n * @module     qbank_viewquestiontype/datafilter/filtertypes/type\n * <AUTHOR> <<EMAIL>>\n * @copyright  2024 Catalyst IT Europe Ltd\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Filter from 'core/datafilter/filtertype';\n\nexport default class extends Filter {\n    /**\n     * Get the list of values for this filter type.\n     *\n     * Overrides the default behaviour of running parseInt on the raw values, since we have textual\n     * plugin identifiers and not numeric IDs.\n     *\n     * @returns {Array}\n     */\n    get values() {\n        return this.rawValues;\n    }\n}\n"], "names": ["Filter", "values", "this", "rawValues"], "mappings": ";;;;;;;;4KA0B6BA,oBASrBC,oBACOC,KAAKC"}