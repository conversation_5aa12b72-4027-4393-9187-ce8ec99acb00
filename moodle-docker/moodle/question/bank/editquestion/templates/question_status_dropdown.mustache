{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qbank_editquestion/question_status_dropdown

    Dropdrown selector for question status in question bank.

    Context variables required for this template:
    * options - list of options for question status containing name, value, selected.
    * questionid - Question id for status selection.

    Example context (json):
    {
        "options" : [ { "name": "Ready", "value": "ready", "selected": true } ],
        "questionid" : "12"
    }
}}
<select id="question_status_dropdown-{{questionid}}" class="form-select my-2" name="question_status_dropdown">
    {{#options}}
        <option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
    {{/options}}
</select>
