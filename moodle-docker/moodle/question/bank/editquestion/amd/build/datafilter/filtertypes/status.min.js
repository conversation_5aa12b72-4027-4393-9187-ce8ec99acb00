define("qbank_editquestion/datafilter/filtertypes/status",["exports","core/datafilter/filtertypes/binary","core/str"],(function(_exports,_binary,_str){var obj;
/**
   * Filter for Status - Ready/Draft
   *
   * @module     qbank_editquestion/datafilter/filtertypes/status
   * <AUTHOR> <<EMAIL>>
   * @copyright  2024 Catalyst IT Europe Ltd
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_binary=(obj=_binary)&&obj.__esModule?obj:{default:obj};class _default extends _binary.default{getTextValues(){return(0,_str.get_strings)([{key:"questionstatusready",component:"qbank_editquestion"},{key:"questionstatusdraft",component:"qbank_editquestion"}])}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=status.min.js.map