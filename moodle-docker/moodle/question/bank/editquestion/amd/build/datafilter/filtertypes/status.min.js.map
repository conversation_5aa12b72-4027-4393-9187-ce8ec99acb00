{"version": 3, "file": "status.min.js", "sources": ["../../../src/datafilter/filtertypes/status.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Filter for Status - Ready/Draft\n *\n * @module     qbank_editquestion/datafilter/filtertypes/status\n * <AUTHOR> <<EMAIL>>\n * @copyright  2024 Catalyst IT Europe Ltd\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Binary from 'core/datafilter/filtertypes/binary';\nimport {get_strings as getStrings} from 'core/str';\n\nexport default class extends Binary {\n    /**\n     * Return strings for Draft/Ready statuses.\n     *\n     * @returns {Promise}\n     */\n    getTextValues() {\n        return getStrings([\n            {key: 'questionstatusready', component: 'qbank_editquestion'},\n            {key: 'questionstatusdraft', component: 'qbank_editquestion'}\n        ]);\n    }\n}\n"], "names": ["Binary", "getTextValues", "key", "component"], "mappings": ";;;;;;;;oKA2B6BA,gBAMzBC,uBACW,oBAAW,CACd,CAACC,IAAK,sBAAuBC,UAAW,sBACxC,CAACD,IAAK,sBAAuBC,UAAW"}