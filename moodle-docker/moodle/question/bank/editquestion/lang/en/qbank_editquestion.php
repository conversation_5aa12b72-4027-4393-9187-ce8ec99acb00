<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component qbank_editquestion, language 'en'.
 *
 * @package    qbank_editquestion
 * @copyright  2021 Catalyst IT Australia Pty Ltd
 * <AUTHOR> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['filter:status'] = 'Status of latest version';
$string['filter:invalidstatus'] = 'Invalid value "{$a}" for status filter.';

$string['pluginname'] = 'Edit questions';
$string['privacy:metadata'] = 'The Edit questions question bank plugin does not store any personal data.';

// Question status.
$string['questionstatus'] = 'Status';
$string['questionstatusready'] = 'Ready';
$string['questionstatushidden'] = 'Hidden';
$string['questionstatusdraft'] = 'Draft';
$string['questionstatusheader'] = 'Change question status';
$string['unrecognizedstatus'] = 'Unrecognised status';

// Edit form.
$string['versioninfo'] = 'Version';
$string['status'] = 'Question status';
