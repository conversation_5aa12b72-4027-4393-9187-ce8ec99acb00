{"version": 3, "file": "addcategory.min.js", "sources": ["../src/addcategory.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Add category button for displaying the modal form.\n *\n * This just connects up the button to the showEditModal listener.\n *\n * @module     qbank_managecategories/addcategory\n * @class      qbank_managecategories/addcategory\n */\n\nimport {BaseComponent} from 'core/reactive';\nimport {categorymanager} from 'qbank_managecategories/categorymanager';\n\nexport default class extends BaseComponent {\n\n    create(descriptor) {\n        this.name = descriptor.element.id;\n        this.selectors = {\n            ADD_BUTTON: '[data-action=\"addeditcategory\"]',\n        };\n    }\n\n    stateReady() {\n        this.addEventListener(this.getElement(this.selectors.ADD_BUTTON), 'click', categorymanager.showEditModal);\n    }\n\n    /**\n     * Static method to create a component instance form the mustache template.\n     *\n     * @param {string} target the DOM main element or its ID\n     * @param {object} selectors optional css selector overrides\n     * @return {Component}\n     */\n    static init(target, selectors) {\n        const targetElement = document.querySelector(target);\n        return new this({\n            element: targetElement,\n            selectors,\n            reactive: categorymanager,\n        });\n    }\n}\n"], "names": ["BaseComponent", "create", "descriptor", "name", "element", "id", "selectors", "ADD_BUTTON", "stateReady", "addEventListener", "this", "getElement", "categorymanager", "showEditModal", "target", "document", "querySelector", "reactive"], "mappings": "uQA2B6BA,wBAEzBC,OAAOC,iBACEC,KAAOD,WAAWE,QAAQC,QAC1BC,UAAY,CACbC,WAAY,mCAIpBC,kBACSC,iBAAiBC,KAAKC,WAAWD,KAAKJ,UAAUC,YAAa,QAASK,iCAAgBC,2BAUnFC,OAAQR,kBAET,IAAII,KAAK,CACZN,QAFkBW,SAASC,cAAcF,QAGzCR,UAAAA,UACAW,SAAUL"}