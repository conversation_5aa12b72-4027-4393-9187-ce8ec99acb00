define("qbank_managecategories/addcategory",["exports","core/reactive","qbank_managecategories/categorymanager"],(function(_exports,_reactive,_categorymanager){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;class _default extends _reactive.BaseComponent{create(descriptor){this.name=descriptor.element.id,this.selectors={ADD_BUTTON:'[data-action="addeditcategory"]'}}stateReady(){this.addEventListener(this.getElement(this.selectors.ADD_BUTTON),"click",_categorymanager.categorymanager.showEditModal)}static init(target,selectors){return new this({element:document.querySelector(target),selectors:selectors,reactive:_categorymanager.categorymanager})}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=addcategory.min.js.map