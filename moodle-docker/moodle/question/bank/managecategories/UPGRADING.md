# qbank_managecategories Upgrade notes

## 4.5

### Changed

- The `\qbank_managecategories\question_category_object` class has been deprecated.
  Methods previously part of this class have been moved to either

   - `\qbank_managecategories\question_categories`,
    for the parts used within this plugin for display a list of categories; or

  `\core_question\category_manager`,
    for the parts used for generate CRUD operations on question categories, including outside of this plugin.

  This change will allow `\qbank_managecategories\question_category_object` to be deprecated, and avoids other parts of the system wishing to manipulate question categories from having to violate cross-component communication rules.

  For more information see [MDL-72397](https://tracker.moodle.org/browse/MDL-72397)

### Deprecated

- Category lists are now generated by templates. The following classes have been deprecated:
  - `\qbank_managecategories\question_category_list`
  - `\qbank_managecategories\question_category_list_item`

  For more information see [MDL-72397](https://tracker.moodle.org/browse/MDL-72397)
- The following methods of `\qbank_managecategories\helper`have been deprecated and moved to
  `\core_question\category_manager`:

  | Method                                               | Replacement                                                                  |
  | ---                                                  | ---                                                                          |
  | `question_is_only_child_of_top_category_in_context`  | `\core_question\category_manager::is_only_child_of_top_category_in_context`  |
  | `question_is_top_category`                           | `\core_question\category_manager::is_top_category`                           |
  | `question_can_delete_cat`                            | `\core_question\category_manager::can_delete_cat`                            |

  For more information see [MDL-72397](https://tracker.moodle.org/browse/MDL-72397)
- `\qbank_managecategories\question_category_object` is now completely deprecated. Its methods have either been migrated to `\qbank_managecategories\question_categories`, `\core_question\category_manager`, or are no longer used at all.

  For more information see [MDL-72397](https://tracker.moodle.org/browse/MDL-72397)
