@qbank @qbank_managecategories @view_manage_categories_plugin @javascript
Feature: Use the qbank plugin manager page for managecategories
  In order to check the plugin behaviour with enable and disable

  Background:
    Given the following "courses" exist:
      | fullname | shortname | category |
      | Course 1 | C1        | 0        |
    And the following "activities" exist:
      | activity | name      | course | idnumber |
      | quiz     | Test quiz | C1     | quiz1    |
    And the following "question categories" exist:
      | contextlevel    | reference | name           |
      | Activity module | quiz1     | Test questions |
    And the following "questions" exist:
      | questioncategory | qtype     | name           | questiontext              |
      | Test questions   | truefalse | First question | Answer the first question |

  Scenario: Enable/disable managecategories plugin from the base view
    Given I log in as "admin"
    When I navigate to "Plugins > Question bank plugins > Manage question bank plugins" in site administration
    And I should see "Manage categories"
    And I click on "Disable" "link" in the "Manage categories" "table_row"
    And I am on the "Test quiz" "quiz activity" page
    Then "Categories" "link" should not exist in current page administration
    And I navigate to "Plugins > Question bank plugins > Manage question bank plugins" in site administration
    And I click on "Enable" "link" in the "Manage categories" "table_row"
    And I am on the "Test quiz" "mod_quiz > question bank" page
    And I should see "Categories" in the "Question bank tertiary navigation" "select"

  Scenario: Enable/disable the tab New category when trying to add a random question to a quiz
    Given I log in as "admin"
    When I navigate to "Plugins > Question bank plugins > Manage question bank plugins" in site administration
    And I should see "Manage categories"
    And I click on "Disable" "link" in the "Manage categories" "table_row"
    And I am on the "Test quiz" "quiz activity" page
    And I click on "Add question" "link"
    And I open the "last" add to quiz menu
    And I follow "a random question"
    Then I should not see "New category"
    And I click on "cancel" "button"
    And I navigate to "Plugins > Question bank plugins > Manage question bank plugins" in site administration
    And I click on "Enable" "link" in the "Manage categories" "table_row"
    And I am on the "Test quiz" "quiz activity" page
    And I click on "Add question" "link"
    And I open the "last" add to quiz menu
    And I follow "a random question"
    And I should see "New category"
