{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qbank_managecategories/showdescriptions

    Template for displaying the Show descriptions toggle.

    Context variables required for this template:
    * helpstringhead - String header accompanied with it help button.
    * hascapability - Boolean, true if user has capability to add categories.
    * checkbox - Checkbox displaying descriptions.
    * contextid - Context id for js init.
    * cmid - Course module id, if in a plugin.

    Example context (json):
    {
        "id": "showdescriptions-toggle",
        "label": "Show descriptions",
        "checked": true
    }
}}
<div class="col-6 mt-2" id="qbank_managecategories-showdescriptions">
    {{>core/toggle}}
</div>
{{#js}}
    require(['qbank_managecategories/showdescriptions'], function(component) {
        component.init('#qbank_managecategories-showdescriptions');
    });
{{/js}}
