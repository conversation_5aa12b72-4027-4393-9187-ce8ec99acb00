{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qbank_managecategories/newchild

    Drop zone for creating a new child category.

    Context variables required for this template:
    * categoryid - The id of the parent category.

    Example context (json):
    {
        "categoryid": "1",
        "newchildtooltip": "As new child of Example"
    }
}}
<span
    class="qbank_managecategories-newchild"
    data-parent="{{categoryid}}"
    data-bs-original-title="{{newchildtooltip}}"
    data-bs-toggle="tooltip"
    data-bs-trigger="manual"
    data-bs-placement="top"
>
    +
</span>

{{#js}}
    require(['qbank_managecategories/newchild'], function(component) {
        component.init('.qbank_managecategories-newchild[data-parent="{{categoryid}}"]');
    });
{{/js}}
