<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace qbank_viewquestionname;

use core_question\local\bank\plugin_features_base;
use core_question\local\bank\view;

/**
 * Plugin entrypoint for columns.
 *
 * @package    qbank_viewquestionname
 * @copyright  2021 Catalyst IT Australia Pty Ltd
 * <AUTHOR> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class plugin_feature extends plugin_features_base {
    #[\Override]
    public function get_question_columns($qbank): array {
        return [
            new question_name_idnumber_tags_column($qbank)
        ];
    }

    #[\Override]
    public function get_question_filters(?view $qbank = null): array {
        return [
            new question_name_condition($qbank),
            new question_idnumber_condition($qbank),
        ];
    }
}
