<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component qbank_viewquestionname, language 'en'
 *
 * @package    qbank_viewquestionname
 * @copyright  2021 Catalyst IT Australia Pty Ltd
 * <AUTHOR> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'View question name';
$string['privacy:metadata'] = 'The View question name question bank plugin does not store any personal data.';
$string['questionidnumbercondition'] = 'Question ID number';
$string['questionnamecondition'] = 'Question name';
// In place editing.
$string['edit_question_name_hint'] = 'Edit question name';
$string['edit_question_name_label'] = 'New value for {$a->name}';
