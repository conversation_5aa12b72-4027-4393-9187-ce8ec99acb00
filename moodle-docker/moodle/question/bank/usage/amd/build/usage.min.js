define("qbank_usage/usage",["exports","core/fragment","core/modal_cancel","core/notification","core/str"],(function(_exports,_fragment,_modal_cancel,_notification,Str){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Usage column selector js.
   *
   * @module     qbank_usage/usage
   * @copyright  2021 Catalyst IT Australia Pty Ltd
   * <AUTHOR> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_fragment=_interopRequireDefault(_fragment),_modal_cancel=_interopRequireDefault(_modal_cancel),_notification=_interopRequireDefault(_notification),Str=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Str);let modal=null;const usageEvent=async(questionId,contextId,specificVersion)=>{const args={questionid:questionId,specificversion:specificVersion};if(null===modal){try{modal=await _modal_cancel.default.create({title:Str.get_string("usageheader","qbank_usage"),body:_fragment.default.loadFragment("qbank_usage","question_usage",contextId,args),large:!0,show:!0})}catch(e){return void _notification.default.exception(e)}modal.getRoot().on("click","a[href].page-link",(function(e){e.preventDefault();let attr=e.target.getAttribute("href");"#"!==attr&&(args.querystring=attr,modal.setBody(_fragment.default.loadFragment("qbank_usage","question_usage",contextId,args)))})),modal.getRoot().on("change","#question_usage_version_dropdown",(function(e){args.questionid=e.target.value,modal.setBody(_fragment.default.loadFragment("qbank_usage","question_usage",contextId,args))}))}else modal.setBody(_fragment.default.loadFragment("qbank_usage","question_usage",contextId,args)),modal.show()};_exports.init=function(){let specificVersion=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const target=document.querySelector("#categoryquestions");null!==target&&target.addEventListener("click",(e=>{e.target.dataset.target&&e.target.dataset.target.includes("questionusagepreview")&&usageEvent(e.target.dataset.questionid,e.target.dataset.contextid,specificVersion)}))}}));

//# sourceMappingURL=usage.min.js.map