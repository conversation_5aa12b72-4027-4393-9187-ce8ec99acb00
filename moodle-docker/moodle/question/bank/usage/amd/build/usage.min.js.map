{"version": 3, "file": "usage.min.js", "sources": ["../src/usage.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Usage column selector js.\n *\n * @module     qbank_usage/usage\n * @copyright  2021 Catalyst IT Australia Pty Ltd\n * <AUTHOR> <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Fragment from 'core/fragment';\nimport ModalCancel from 'core/modal_cancel';\nimport Notification from 'core/notification';\nimport * as Str from 'core/str';\n\nlet modal = null;\n\n/**\n * Event listeners for the module.\n *\n * @method clickEvent\n * @param {int} questionId\n * @param {int} contextId\n * @param {boolean} specificVersion Is the view listing specific question versions?\n */\nconst usageEvent = async(questionId, contextId, specificVersion) => {\n    const args = {\n        questionid: questionId,\n        specificversion: specificVersion,\n    };\n    if (modal === null) {\n        try {\n            modal = await ModalCancel.create({\n                title: Str.get_string('usageheader', 'qbank_usage'),\n                body: Fragment.loadFragment('qbank_usage', 'question_usage', contextId, args),\n                large: true,\n                show: true,\n            });\n        } catch (e) {\n            Notification.exception(e);\n            return;\n        }\n\n        modal.getRoot().on('click', 'a[href].page-link', function(e) {\n            e.preventDefault();\n            let attr = e.target.getAttribute(\"href\");\n            if (attr !== '#') {\n                args.querystring = attr;\n                modal.setBody(Fragment.loadFragment('qbank_usage', 'question_usage', contextId, args));\n            }\n        });\n        // Version selection event.\n        modal.getRoot().on('change', '#question_usage_version_dropdown', function(e) {\n            args.questionid = e.target.value;\n            modal.setBody(Fragment.loadFragment('qbank_usage', 'question_usage', contextId, args));\n        });\n    } else {\n        modal.setBody(Fragment.loadFragment('qbank_usage', 'question_usage', contextId, args));\n        modal.show();\n    }\n\n};\n\n/**\n * Entrypoint of the js.\n *\n * @method init\n * @param {boolean} specificVersion Is the view listing specific question versions?\n */\nexport const init = (specificVersion = false) => {\n    const target = document.querySelector('#categoryquestions');\n    if (target !== null) {\n        target.addEventListener('click', (e) => {\n            if (e.target.dataset.target && e.target.dataset.target.includes('questionusagepreview')) {\n                // Call for the event listener to listed for clicks in any usage count row.\n                usageEvent(e.target.dataset.questionid, e.target.dataset.contextid, specificVersion);\n            }\n        });\n    }\n};\n"], "names": ["modal", "usageEvent", "async", "questionId", "contextId", "specificVersion", "args", "questionid", "specificversion", "ModalCancel", "create", "title", "Str", "get_string", "body", "Fragment", "loadFragment", "large", "show", "e", "exception", "getRoot", "on", "preventDefault", "attr", "target", "getAttribute", "querystring", "setBody", "value", "document", "querySelector", "addEventListener", "dataset", "includes", "contextid"], "mappings": ";;;;;;;;g4BA6BIA,MAAQ,WAUNC,WAAaC,MAAMC,WAAYC,UAAWC,yBACtCC,KAAO,CACTC,WAAYJ,WACZK,gBAAiBH,oBAEP,OAAVL,MAAgB,KAEZA,YAAcS,sBAAYC,OAAO,CAC7BC,MAAOC,IAAIC,WAAW,cAAe,eACrCC,KAAMC,kBAASC,aAAa,cAAe,iBAAkBZ,UAAWE,MACxEW,OAAO,EACPC,MAAM,IAEZ,MAAOC,qCACQC,UAAUD,GAI3BnB,MAAMqB,UAAUC,GAAG,QAAS,qBAAqB,SAASH,GACtDA,EAAEI,qBACEC,KAAOL,EAAEM,OAAOC,aAAa,QACpB,MAATF,OACAlB,KAAKqB,YAAcH,KACnBxB,MAAM4B,QAAQb,kBAASC,aAAa,cAAe,iBAAkBZ,UAAWE,WAIxFN,MAAMqB,UAAUC,GAAG,SAAU,oCAAoC,SAASH,GACtEb,KAAKC,WAAaY,EAAEM,OAAOI,MAC3B7B,MAAM4B,QAAQb,kBAASC,aAAa,cAAe,iBAAkBZ,UAAWE,eAGpFN,MAAM4B,QAAQb,kBAASC,aAAa,cAAe,iBAAkBZ,UAAWE,OAChFN,MAAMkB,sBAWM,eAACb,8EACXoB,OAASK,SAASC,cAAc,sBACvB,OAAXN,QACAA,OAAOO,iBAAiB,SAAUb,IAC1BA,EAAEM,OAAOQ,QAAQR,QAAUN,EAAEM,OAAOQ,QAAQR,OAAOS,SAAS,yBAE5DjC,WAAWkB,EAAEM,OAAOQ,QAAQ1B,WAAYY,EAAEM,OAAOQ,QAAQE,UAAW9B"}