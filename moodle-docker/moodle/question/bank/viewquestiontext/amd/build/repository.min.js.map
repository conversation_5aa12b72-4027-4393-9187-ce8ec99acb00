{"version": 3, "file": "repository.min.js", "sources": ["../src/repository.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * External function calls for qbank_columnsortorder\n *\n * @module     qbank_viewquestiontext/repository\n * @copyright  2023 Catalyst IT Europe Ltd.\n * <AUTHOR> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {call as fetchMany} from 'core/ajax';\n\n/**\n * Set the question text format.\n *\n * @param {Number} format The question text format.\n * @return {Promise}\n */\nexport const setQuestionTextFormat = (format) => fetchMany([{\n    methodname: 'qbank_viewquestiontext_set_question_text_format',\n    args: {\n        format,\n    },\n}])[0];\n"], "names": ["format", "methodname", "args"], "mappings": "0NAgCsCA,SAAW,cAAU,CAAC,CACxDC,WAAY,kDACZC,KAAM,CACFF,OAAAA,WAEJ"}