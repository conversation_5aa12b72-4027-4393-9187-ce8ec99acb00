{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qbank_tagquestion/tag_condition

    An auto-complete select box containing a list of available tags to
    filter the quesiton bank questions by.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * tagoptions A list of available tags

    Example context (json):
    {
        "tagoptions": [
            {
                "id": 1,
                "name": "foo",
                "selected": true
            },
            {
                "id": 2,
                "name": "bar",
                "selected": false
            }
        ]
    }
}}
<div class="tag-condition-container" data-region="tag-condition-container-{{uniqid}}">
    <div class="mb-3">
        <select multiple name="qtagids[]" class="form-control invisible" size="3" data-region="tag-select">
            {{#tagoptions}}
                <option {{#selected}}selected{{/selected}} value="{{id}}">{{name}}</option>
            {{/tagoptions}}
        </select>
        {{< core/overlay_loading }}
            {{$hiddenclass}}{{/hiddenclass}}
        {{/ core/overlay_loading }}
    </div>
</div>
{{#js}}
require(
[
    'jquery',
    'core/form-autocomplete'
],
function(
    $,
    AutoComplete
) {
    var root = $('[data-region="tag-condition-container-{{uniqid}}"]');
    var selectElement = root.find('[data-region="tag-select"]');
    var loadingContainer = root.find('[data-region="overlay-icon-container"]');
    var placeholderText = {{#quote}}{{#str}} filterbytags, core_question {{/str}}{{/quote}};
    var noSelectionText = {{#quote}}{{#str}} notagfiltersapplied, core_question {{/str}}{{/quote}};

    AutoComplete.enhance(
        selectElement, // Element to enhance.
        false, // Don't allow support for creating new tags.
        false, // Don't allow AMD module to handle loading new tags.
        placeholderText, // Placeholder text.
        false, // Make search case insensitive.
        true, // Show suggestions for tags.
        noSelectionText // Text when no tags are selected.
    ).always(function() {
        // Hide the loading icon once the autocomplete has initialised.
        loadingContainer.addClass('hidden');
    });

    // We need to trigger a form submission because of how the question bank
    // page handles reloading the questions when an option changes.
    selectElement.on('change', function() {
        selectElement.closest('form').submit();
    });
});
{{/js}}
