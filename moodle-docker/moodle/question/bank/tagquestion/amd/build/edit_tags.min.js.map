{"version": 3, "file": "edit_tags.min.js", "sources": ["../src/edit_tags.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * A javascript module to handle question tags editing.\n *\n * @module     qbank_tagquestion/edit_tags\n * @copyright  2018 Simey Lameze <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine([\n            'jquery',\n            'core/fragment',\n            'core/str',\n            'core/modal_events',\n            'core/modal_save_cancel',\n            'core/notification',\n            'core/custom_interaction_events',\n            'qbank_tagquestion/repository',\n            'qbank_tagquestion/selectors',\n        ],\n        function(\n            $,\n            Fragment,\n            Str,\n            ModalEvents,\n            ModalSaveCancel,\n            Notification,\n            CustomEvents,\n            Repository,\n            QuestionSelectors\n        ) {\n\n    /**\n     * Enable the save button in the footer.\n     *\n     * @param {object} root The container element.\n     * @method enableSaveButton\n     */\n    var enableSaveButton = function(root) {\n        root.find(QuestionSelectors.actions.save).prop('disabled', false);\n    };\n\n    /**\n     * Disable the save button in the footer.\n     *\n     * @param {object} root The container element.\n     * @method disableSaveButton\n     */\n    var disableSaveButton = function(root) {\n        root.find(QuestionSelectors.actions.save).prop('disabled', true);\n    };\n\n    /**\n     * Get the serialised form data.\n     *\n     * @method getFormData\n     * @param {object} modal The modal object.\n     * @return {string} serialised form data\n     */\n    var getFormData = function(modal) {\n        return modal.getBody().find('form').serialize();\n    };\n\n    /**\n     * Set the element state to loading.\n     *\n     * @param {object} root The container element\n     * @method startLoading\n     */\n    var startLoading = function(root) {\n        var loadingIconContainer = root.find(QuestionSelectors.containers.loadingIcon);\n\n        loadingIconContainer.removeClass('hidden');\n    };\n\n    /**\n     * Remove the loading state from the element.\n     *\n     * @param {object} root The container element\n     * @method stopLoading\n     */\n    var stopLoading = function(root) {\n        var loadingIconContainer = root.find(QuestionSelectors.containers.loadingIcon);\n\n        loadingIconContainer.addClass('hidden');\n    };\n\n    /**\n     * Set the context Id data attribute on the modal.\n     *\n     * @param {Promise} modal The modal promise.\n     * @param {int} contextId The context id.\n     */\n    var setContextId = function(modal, contextId) {\n        modal.getBody().attr('data-contextid', contextId);\n    };\n\n    /**\n     * Get the context Id data attribute value from the modal body.\n     *\n     * @param {Promise} modal The modal promise.\n     * @return {int} The context id.\n     */\n    var getContextId = function(modal) {\n        return modal.getBody().data('contextid');\n    };\n\n    /**\n     * Set the question Id data attribute on the modal.\n     *\n     * @param {Promise} modal The modal promise.\n     * @param {int} questionId The question Id.\n     */\n    var setQuestionId = function(modal, questionId) {\n        modal.getBody().attr('data-questionid', questionId);\n    };\n\n    /**\n     * Get the question Id data attribute value from the modal body.\n     *\n     * @param {Promise} modal The modal promise.\n     * @return {int} The question Id.\n     */\n    var getQuestionId = function(modal) {\n        return modal.getBody().data('questionid');\n    };\n\n    /**\n     * Register event listeners for the module.\n     *\n     * @param {object} root The calendar root element\n     */\n    var registerEventListeners = function(root) {\n        var modalPromise = ModalSaveCancel.create({\n            large: false,\n        }).then(function(modal) {\n            // All of this code only executes once, when the modal is\n            // first created. This allows us to add any code that should\n            // only be run once, such as adding event handlers to the modal.\n            Str.get_string('questiontags', 'question')\n                .then(function(string) {\n                    modal.setTitle(string);\n                    return string;\n                })\n                .catch(Notification.exception);\n\n            modal.getRoot().on(ModalEvents.save, function(e) {\n                var form = modal.getBody().find('form');\n                form.submit();\n                e.preventDefault();\n            });\n\n            modal.getRoot().on('submit', 'form', function(e) {\n                save(modal, root).then(function() {\n                    modal.hide();\n                    location.reload();\n                    return;\n                }).catch(Notification.exception);\n\n                // Stop the form from actually submitting and prevent it's\n                // propagation because we have already handled the event.\n                e.preventDefault();\n                e.stopPropagation();\n            });\n\n            return modal;\n        });\n\n        root.on('click', QuestionSelectors.actions.edittags, function(e) {\n            e.preventDefault();\n            // eslint-disable-next-line promise/catch-or-return\n            modalPromise.then((modal) => modal.show());\n        });\n\n        // We need to add an event handler to the tags link because there are\n        // multiple links on the page and without adding a listener we don't know\n        // which one the user clicked on the show the modal.\n        root.on(CustomEvents.events.activate, QuestionSelectors.actions.edittags, function(e) {\n            var currentTarget = $(e.currentTarget);\n\n            var questionId = currentTarget.data('questionid'),\n                canTag = !!currentTarget.data('cantag'),\n                contextId = currentTarget.data('contextid');\n\n            // This code gets called each time the user clicks the tag link\n            // so we can use it to reload the contents of the tag modal.\n            modalPromise.then(function(modal) {\n                // Display spinner and disable save button.\n                disableSaveButton(root);\n                startLoading(root);\n\n                var args = {\n                    id: questionId\n                };\n\n                var tagsFragment = Fragment.loadFragment('qbank_tagquestion', 'tags_form', contextId, args);\n                modal.setBody(tagsFragment);\n\n                tagsFragment.then(function() {\n                        enableSaveButton(root);\n                        return;\n                    })\n                    .always(function() {\n                        // Always hide the loading spinner when the request\n                        // has completed.\n                        stopLoading(root);\n                        return;\n                    })\n                .catch(Notification.exception);\n\n                // Show or hide the save button depending on whether the user\n                // has the capability to edit the tags.\n                if (canTag) {\n                    modal.getRoot().find(QuestionSelectors.actions.save).show();\n                } else {\n                    modal.getRoot().find(QuestionSelectors.actions.save).hide();\n                }\n\n                setQuestionId(modal, questionId);\n                setContextId(modal, contextId);\n\n                return modal;\n            }).catch(Notification.exception);\n\n            e.preventDefault();\n        });\n    };\n\n    /**\n     * Send the form data to the server to save question tags.\n     *\n     * @method save\n     * @param {object} modal The modal object.\n     * @param {object} root The container element.\n     * @return {object} A promise\n     */\n    var save = function(modal, root) {\n        // Display spinner and disable save button.\n        disableSaveButton(root);\n        startLoading(root);\n\n        var formData = getFormData(modal);\n        var questionId = getQuestionId(modal);\n        var contextId = getContextId(modal);\n\n        // Send the form data to the server for processing.\n        return Repository.submitTagCreateUpdateForm(questionId, contextId, formData)\n            .always(function() {\n                // Regardless of success or error we should always stop\n                // the loading icon and re-enable the buttons.\n                stopLoading(root);\n                enableSaveButton(root);\n                return;\n            })\n            .catch(Notification.exception);\n    };\n\n    return {\n        init: function(root) {\n            root = $(root);\n            registerEventListeners(root);\n        }\n    };\n});\n"], "names": ["define", "$", "Fragment", "Str", "ModalEvents", "ModalSaveCancel", "Notification", "CustomEvents", "Repository", "QuestionSelectors", "enableSaveButton", "root", "find", "actions", "save", "prop", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "startLoading", "containers", "loadingIcon", "removeClass", "stopLoading", "addClass", "modal", "formData", "getBody", "serialize", "getFormData", "questionId", "data", "getQuestionId", "contextId", "getContextId", "submitTagCreateUpdateForm", "always", "catch", "exception", "init", "modalPromise", "create", "large", "then", "get_string", "string", "setTitle", "getRoot", "on", "e", "submit", "preventDefault", "hide", "location", "reload", "stopPropagation", "edittags", "show", "events", "activate", "currentTarget", "canTag", "args", "id", "tagsFragment", "loadFragment", "setBody", "attr", "setQuestionId", "setContextId", "registerEventListeners"], "mappings": ";;;;;;;AAsBAA,qCAAO,CACK,SACA,gBACA,WACA,oBACA,yBACA,oBACA,iCACA,+BACA,gCAEJ,SACIC,EACAC,SACAC,IACAC,YACAC,gBACAC,aACAC,aACAC,WACAC,uBASJC,iBAAmB,SAASC,MAC5BA,KAAKC,KAAKH,kBAAkBI,QAAQC,MAAMC,KAAK,YAAY,IAS3DC,kBAAoB,SAASL,MAC7BA,KAAKC,KAAKH,kBAAkBI,QAAQC,MAAMC,KAAK,YAAY,IAoB3DE,aAAe,SAASN,MACGA,KAAKC,KAAKH,kBAAkBS,WAAWC,aAE7CC,YAAY,WASjCC,YAAc,SAASV,MACIA,KAAKC,KAAKH,kBAAkBS,WAAWC,aAE7CG,SAAS,WAwJ9BR,KAAO,SAASS,MAAOZ,MAEvBK,kBAAkBL,MAClBM,aAAaN,UAETa,SAtLU,SAASD,cAChBA,MAAME,UAAUb,KAAK,QAAQc,YAqLrBC,CAAYJ,OACvBK,WAvHY,SAASL,cAClBA,MAAME,UAAUI,KAAK,cAsHXC,CAAcP,OAC3BQ,UA5IW,SAASR,cACjBA,MAAME,UAAUI,KAAK,aA2IZG,CAAaT,cAGtBf,WAAWyB,0BAA0BL,WAAYG,UAAWP,UAC9DU,QAAO,WAGJb,YAAYV,MACZD,iBAAiBC,SAGpBwB,MAAM7B,aAAa8B,kBAGrB,CACHC,KAAM,SAAS1B,OA9HU,SAASA,UAC9B2B,aAAejC,gBAAgBkC,OAAO,CACtCC,OAAO,IACRC,MAAK,SAASlB,cAIbpB,IAAIuC,WAAW,eAAgB,YAC1BD,MAAK,SAASE,eACXpB,MAAMqB,SAASD,QACRA,UAEVR,MAAM7B,aAAa8B,WAExBb,MAAMsB,UAAUC,GAAG1C,YAAYU,MAAM,SAASiC,GAC/BxB,MAAME,UAAUb,KAAK,QAC3BoC,SACLD,EAAEE,oBAGN1B,MAAMsB,UAAUC,GAAG,SAAU,QAAQ,SAASC,GAC1CjC,KAAKS,MAAOZ,MAAM8B,MAAK,WACnBlB,MAAM2B,OACNC,SAASC,YAEVjB,MAAM7B,aAAa8B,WAItBW,EAAEE,iBACFF,EAAEM,qBAGC9B,SAGXZ,KAAKmC,GAAG,QAASrC,kBAAkBI,QAAQyC,UAAU,SAASP,GAC1DA,EAAEE,iBAEFX,aAAaG,MAAMlB,OAAUA,MAAMgC,YAMvC5C,KAAKmC,GAAGvC,aAAaiD,OAAOC,SAAUhD,kBAAkBI,QAAQyC,UAAU,SAASP,OAC3EW,cAAgBzD,EAAE8C,EAAEW,eAEpB9B,WAAa8B,cAAc7B,KAAK,cAChC8B,SAAWD,cAAc7B,KAAK,UAC9BE,UAAY2B,cAAc7B,KAAK,aAInCS,aAAaG,MAAK,SAASlB,OAEvBP,kBAAkBL,MAClBM,aAAaN,UAETiD,KAAO,CACPC,GAAIjC,YAGJkC,aAAe5D,SAAS6D,aAAa,oBAAqB,YAAahC,UAAW6B,aACtFrC,MAAMyC,QAAQF,cAEdA,aAAarB,MAAK,WACV/B,iBAAiBC,SAGpBuB,QAAO,WAGJb,YAAYV,SAGnBwB,MAAM7B,aAAa8B,WAIhBuB,OACApC,MAAMsB,UAAUjC,KAAKH,kBAAkBI,QAAQC,MAAMyC,OAErDhC,MAAMsB,UAAUjC,KAAKH,kBAAkBI,QAAQC,MAAMoC,OAtGjD,SAAS3B,MAAOK,YAChCL,MAAME,UAAUwC,KAAK,kBAAmBrC,YAwGhCsC,CAAc3C,MAAOK,YA7Hd,SAASL,MAAOQ,WAC/BR,MAAME,UAAUwC,KAAK,iBAAkBlC,WA6H/BoC,CAAa5C,MAAOQ,WAEbR,SACRY,MAAM7B,aAAa8B,WAEtBW,EAAEE,oBAoCFmB,CADAzD,KAAOV,EAAEU"}