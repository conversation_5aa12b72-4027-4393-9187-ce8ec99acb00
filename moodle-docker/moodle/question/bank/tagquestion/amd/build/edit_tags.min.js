/**
 * A javascript module to handle question tags editing.
 *
 * @module     qbank_tagquestion/edit_tags
 * @copyright  2018 <PERSON><PERSON><PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("qbank_tagquestion/edit_tags",["jquery","core/fragment","core/str","core/modal_events","core/modal_save_cancel","core/notification","core/custom_interaction_events","qbank_tagquestion/repository","qbank_tagquestion/selectors"],(function($,Fragment,Str,ModalEvents,ModalSaveCancel,Notification,CustomEvents,Repository,QuestionSelectors){var enableSaveButton=function(root){root.find(QuestionSelectors.actions.save).prop("disabled",!1)},disableSaveButton=function(root){root.find(QuestionSelectors.actions.save).prop("disabled",!0)},startLoading=function(root){root.find(QuestionSelectors.containers.loadingIcon).removeClass("hidden")},stopLoading=function(root){root.find(QuestionSelectors.containers.loadingIcon).addClass("hidden")},save=function(modal,root){disableSaveButton(root),startLoading(root);var formData=function(modal){return modal.getBody().find("form").serialize()}(modal),questionId=function(modal){return modal.getBody().data("questionid")}(modal),contextId=function(modal){return modal.getBody().data("contextid")}(modal);return Repository.submitTagCreateUpdateForm(questionId,contextId,formData).always((function(){stopLoading(root),enableSaveButton(root)})).catch(Notification.exception)};return{init:function(root){!function(root){var modalPromise=ModalSaveCancel.create({large:!1}).then((function(modal){return Str.get_string("questiontags","question").then((function(string){return modal.setTitle(string),string})).catch(Notification.exception),modal.getRoot().on(ModalEvents.save,(function(e){modal.getBody().find("form").submit(),e.preventDefault()})),modal.getRoot().on("submit","form",(function(e){save(modal,root).then((function(){modal.hide(),location.reload()})).catch(Notification.exception),e.preventDefault(),e.stopPropagation()})),modal}));root.on("click",QuestionSelectors.actions.edittags,(function(e){e.preventDefault(),modalPromise.then((modal=>modal.show()))})),root.on(CustomEvents.events.activate,QuestionSelectors.actions.edittags,(function(e){var currentTarget=$(e.currentTarget),questionId=currentTarget.data("questionid"),canTag=!!currentTarget.data("cantag"),contextId=currentTarget.data("contextid");modalPromise.then((function(modal){disableSaveButton(root),startLoading(root);var args={id:questionId},tagsFragment=Fragment.loadFragment("qbank_tagquestion","tags_form",contextId,args);return modal.setBody(tagsFragment),tagsFragment.then((function(){enableSaveButton(root)})).always((function(){stopLoading(root)})).catch(Notification.exception),canTag?modal.getRoot().find(QuestionSelectors.actions.save).show():modal.getRoot().find(QuestionSelectors.actions.save).hide(),function(modal,questionId){modal.getBody().attr("data-questionid",questionId)}(modal,questionId),function(modal,contextId){modal.getBody().attr("data-contextid",contextId)}(modal,contextId),modal})).catch(Notification.exception),e.preventDefault()}))}(root=$(root))}}}));

//# sourceMappingURL=edit_tags.min.js.map