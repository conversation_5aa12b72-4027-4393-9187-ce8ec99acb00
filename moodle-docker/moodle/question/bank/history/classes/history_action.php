<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace qbank_history;

use core_question\local\bank\filter_condition_manager;
use core_question\local\bank\question_action_base;

/**
 * Question bank column for the history action icon.
 *
 * @package    qbank_history
 * @copyright  2022 Catalyst IT Australia Pty Ltd
 * <AUTHOR> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class history_action extends question_action_base {

    // Store this lang string for performance.
    protected $strpreview;

    public function init(): void {
        parent::init();
        $this->strpreview = get_string('history_action', 'qbank_history');
    }

    public function get_menu_position(): int {
        return 500;
    }

    protected function get_url_icon_and_label(\stdClass $question): array {
        if (!\question_bank::is_qtype_installed($question->qtype)) {
            // It sometimes happens that people end up with junk questions
            // in their question bank of a type that is no longer installed.
            // We cannot do most actions on them, because that leads to errors.
            return [null, null, null];
        }

        if (question_has_capability_on($question, 'use')) {
            $currentfilter = $this->qbank->base_url()->param('filter');
            if ($currentfilter) {
                $currentfilter = filter_condition_manager::update_filter_param_to_category(
                    $currentfilter, $question->categoryid);
            } else if (empty($question->isdummy)) {
                $currentfilter = json_encode(filter_condition_manager::get_default_filter(
                    $question->categoryid . ',' . $question->contextid));
            }

            $url = helper::get_question_history_url(
                $question->questionbankentryid,
                $this->qbank->returnurl,
                $this->qbank->cm->id,
                $currentfilter,
            );
            return [$url, 't/log', $this->strpreview];
        }

        return [null, null, null];
    }

}
