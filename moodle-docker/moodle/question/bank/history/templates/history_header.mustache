{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qbank_history/history_header

    The header of the history page.
    * returnurl - The url of the page to return to, usually the base qbank page
    * questionincon - The icon of the question type
    * questionname - The name of the latest question version

    Example context (json):
    {
        "returnurl": "https://url/courseid=1",
        "questionicon": "<i class='fa fa-address-book'></i>",
        "questionname": "Question 1"
    }
}}

<div class="history-header mb-3">
    <div class="row">
        <div class="col-8 text-start fw-bold">
            <h3>
                {{{questionicon}}}
                {{questionname}}
            </h3>
        </div>
        <div class="col-4 text-end">
            <a class="btn btn-secondary" id="qbank-history-close" href="{{{returnurl}}}">{{#str}} close_history, qbank_history {{/str}}</a>
        </div>
    </div>
</div>
