=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This file describes core qbank plugin changes in /question/bank/*,
information provided here is intended especially for developers.

=== 4.4 ===

* Question bank actions (anything that subclasses question_action_base) should implement the
  method get_menu_position() to control what position in the action menu it appears it.
  See the comment on the base class method for more details. If you don't do this, you will
  get a debugging warning.

=== 4.3 ===

* The helper class in qbank_statistics has had several of its methods deprecated.
  If you want to display statistics in the question bank, you should now override
  the get_required_statistics_fields() method in your column class, and then
  the values you need will be available from $this->qbank->get_aggregate_statistic(...).
  If you are not in a question_bank_column class, you can directly access efficient
  statistics-loading from the core_question\local\statistics\statistics_bulk_loader class.

* Question actions are now implemented as a standalone qbank plugin feature. Instead of passing the actions
  through get_question_columns(), plugins implementing actions will now have to use get_question_actions(). Action classes are
  implemented by extending the new question_action_base class, which replaces the deprecated action_column_base class,
  menu_action_column_base class and menuable_action interface. The shared functionality between actions and columns has now
  been moved to the shared view_component base class.

=== 4.1 ===

* New functions qbank_usage\helper::get_question_bank_usage_sql and
  qbank_usage\helper::get_question_attempt_usage_sql have been implemented.
  When calling a query with the SQL those methods returned, you have to be sure
  in the accompanying $params array are in the right order.

=== 4.0 ===

* This plugin type is new in Moodle 4.0. It is for adding features to the question bank.
  They key entry point is the class plugin_feature in the plugin namespace. This tells
  the core question bank API exactly which features this plugin implements.
  These features can currently be new columns in the question bank display, and bulk actions.
  Currently, actions on single questions are implemented as a type of column (but this
  should probably change in the future.) To get started, have a look at the methods
  in core_question\local\bank\plugin_features_base which you can override.
