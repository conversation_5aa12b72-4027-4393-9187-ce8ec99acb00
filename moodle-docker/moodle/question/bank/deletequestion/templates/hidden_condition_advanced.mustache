{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qbank_deletequestion/hidden_condition_advanced

    Example context (json):
    {
        "displaydata": [
          {
                "checked": "checked attribute"
          }
        ]
    }
}}
<div class="hidden_condition_advanced">
    <input type="hidden" name="showhidden" value="0" id="showhidden_off">
    <input id="showhidden_on" class="searchoptions me-1" type="checkbox" value="1" name="showhidden" {{{checked}}}>
    <label for="showhidden_on">{{#str}} showhidden, question{{/str}}</label>
</div>
