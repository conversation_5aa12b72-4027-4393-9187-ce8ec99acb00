=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes for question import/export format plugins.

=== 4.0 ===

1) The major question bank changes should not affect import/export plugins.
   The navigation changes may affect Behat tests. If you encounter this,
   the best way to fix it is to use the new navigation steps in MDL-74130.

2) The new validate_file() method in question/format.php can be overwritten
   to implement more expensive or detailed file integrity checks for question imports.
   There is a simple way to do this if you just want to check that the file is valid UTF-8,
   which you can see an example of in format_gift.

3) The ExamView question format has been completely removed.
   (The last posts in http://forum.examview.com/index.php?
   are from over 10 years ago and there are no moodle.org
   discussions about Examview in the last 10 years.)

4) The WebCT question format has been completely removed (WebCT was acquired by Blackboard in 2006).


=== 3.6 ===

* Saving question category descriptions (info) is now supported in Moodle XML import/export format.
New xml-structure snippet for a question category:
<question type="category">
    <category>
        <text>${$contexttypename}$/{$category_path}</text>
    </category>
    <info format="{$format}">
        <text>{$info_categorydescription}</text>
    </info>
</question>
* The method importprocess() in question/format.php no longer accepts $category as a parameter.
If required in a plugin then please override this method.

=== 2.3 ===

* This plugin type now supports cron in the standard way. If required, Create a
  lib.php file containing
function qformat_mypluginname_cron() {};

=== 2.1.5 / 2.2.3 / 2.3 ===

* The readquestions method used to take a second argument $context. However, at
  the point where this method was called, it was impossible to know what
  context the quetsions were going to be saved into, so the value could be
  wrong. Also, none of the standard question formats were using this argument,
  so it was removed. See MDL-32220.

=== 2.2 ===

* The plugin name used to be defined in a string called the same thing as the
format, with assoicated help strings, for example:

$string['aiken'] = 'Aiken format';
$string['aiken_help'] = 'This is a simple format ...';
$string['aiken_link'] = 'qformat/aiken';

This needs to be changed to use the standard string name pluginname, as for
other plugin types.

$string['pluginname'] = 'Aiken format';
$string['pluginname_help'] = 'This is a simple format ...';
$string['pluginname_link'] = 'qformat/aiken';
