{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/question_banks_list

    Example context (json):
{
    "purpose": "content",
    "iconurl": {},
    "modname": "Course 1 course question bank",
    "description": "<div class=\"no-overflow\">Description<\/div>",
    "managequestions": {
        "id": "action_link669a882ce8ec613",
        "disabled": false,
        "text": "Course 1 course question bank",
        "url": "example/index.php?id=2",
        "icon": null,
        "classes": "",
        "attributes": [],
        "actions": [],
        "hasactions": false
    },
    "managebank": {
        "instance": 1,
        "classes": "moodle-actionmenu",
        "attributes": [
            {
                "name": "id",
                "value": "action-menu-1"
            },
            {
                "name": "data-enhance",
                "value": "moodle-core-actionmenu"
            }
        ],
        "primary": {
            "title": "",
            "prioritise": false,
            "classes": "menubar",
            "attributes": [
                {
                    "name": "id",
                    "value": "action-menu-1-menubar"
                }
            ],
            "triggerattributes": [],
            "menutrigger": "<i class=\"icon fa fa-ellipsis-v fa-fw \"  title=\"Edit\" role=\"img\" aria-label=\"Edit\"><\/i>",
            "triggerextraclasses": "btn btn-icon d-flex no-caret ",
            "triggerrole": "button",
            "iconraw": "",
            "actiontext": "",
            "items": []
        },
        "secondary": {
            "classes": "menu",
            "attributes": [
                {
                    "name": "id",
                    "value": "action-menu-1-menu"
                },
                {
                    "name": "data-rel",
                    "value": "menu-content"
                },
                {
                    "name": "aria-labelledby",
                    "value": "action-menu-toggle-1"
                },
                {
                    "name": "role",
                    "value": "menu"
                }
            ],
            "items": [
                {
                    "simpleitem": false,
                    "actionmenulink": {
                        "id": "action_link669a882ce8ec67",
                        "disabled": false,
                        "text": "Edit settings",
                        "url": "https:example.com/course/mod.php?sesskey=abcde12345&update=2",
                        "icon": {
                            "key": "t\/edit",
                            "component": "moodle",
                            "title": ""
                        },
                        "classes": "editing_update menu-action",
                        "attributes": [
                            {
                                "name": "data-action",
                                "value": "update"
                            },
                            {
                                "name": "role",
                                "value": "menuitem"
                            },
                            {
                                "name": "tabindex",
                                "value": "-1"
                            }
                        ],
                        "actions": [],
                        "hasactions": false,
                        "showtext": true
                    }
                },
                {
                    "simpleitem": false,
                    "actionmenulink": {
                        "id": "action_link669a882ce8ec611",
                        "disabled": false,
                        "text": "Assign roles",
                        "url": "https:example.com/admin/roles/assign.php?contextid=16",
                        "icon": {
                            "key": "t\/assignroles",
                            "component": "moodle",
                            "title": ""
                        },
                        "classes": "editing_assign menu-action",
                        "attributes": [
                            {
                                "name": "data-action",
                                "value": "assignroles"
                            },
                            {
                                "name": "data-sectionreturn",
                                "value": null
                            },
                            {
                                "name": "role",
                                "value": "menuitem"
                            },
                            {
                                "name": "tabindex",
                                "value": "-1"
                            }
                        ],
                        "actions": [],
                        "hasactions": false,
                        "showtext": true
                    }
                },
                {
                    "simpleitem": false,
                    "actionmenulink": {
                        "id": "action_link669a882ce8ec612",
                        "disabled": false,
                        "text": "Delete",
                        "url": "https:example.com/course/mod.php?sesskey=abcde12345&delete=2",
                        "icon": {
                            "key": "t\/delete",
                            "component": "moodle",
                            "title": ""
                        },
                        "classes": "editing_delete text-danger menu-action",
                        "attributes": [
                            {
                                "name": "data-action",
                                "value": "cmDelete"
                            },
                            {
                                "name": "data-sectionreturn",
                                "value": null
                            },
                            {
                                "name": "data-id",
                                "value": "2"
                            },
                            {
                                "name": "role",
                                "value": "menuitem"
                            },
                            {
                                "name": "tabindex",
                                "value": "-1"
                            }
                        ],
                        "actions": [],
                        "hasactions": false,
                        "showtext": true
                    }
                }
            ]
        },
        "dropdownalignment": "dropdown-menu-right"
    }
}
}}

<div class="question-banks-list">
    <ul class="list-unstyled">
        <li class="d-flex mb-2 me-2">
            <div class="flex-shrink-0 {{purpose}} activityiconcontainer icon-size-6 modicon_{{modname}}">
                <a href="{{managequestions.url}}">
                    <img class="icon activityicon" aria-hidden="true" src="{{iconurl}}" alt="modicon_{{modname}}"/>
                </a>
            </div>
            <div class="flex-grow-1 align-self-center ms-3">
                {{#managequestions}}
                    {{> core/action_link}}
                {{/managequestions}}
                <div class="manage-bank-actions d-inline-block">
                    {{#managebank}}
                        {{> core/action_menu}}
                    {{/managebank}}
                </div>
                {{{description}}}
            </div>
        </li>
    </ul>
</div>
