{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/switch_question_bank

    Example context (json):
{
    "quizname": "Quiz 1",
    "quizcmid": 1,
    "quizcontextid": 1,
    "hascoursesharedbanks": true,
    "coursesharedbanks": [
        {
            "name": "Question bank 1",
            "modid": "2",
            "contextid": 2,
            "coursenamebankname": "c1 - Question bank 1",
            "cminfo": {},
            "questioncategories": []
        },
        {
            "name": "Question bank 2",
            "modid": "3",
            "contextid": 3,
            "coursenamebankname": "c1 - Question bank 2",
            "cminfo": {},
            "questioncategories": []
        }
    ],
    "hasrecentlyviewedbanks": true,
    "recentlyviewedbanks": [
        {
            "name": "Question bank 3",
            "modid": "4",
            "contextid": 4,
            "coursenamebankname": "c2 - Question bank 4",
            "cminfo": {},
            "questioncategories": []
        },
        {
            "name": "Question bank 4",
            "modid": "6",
            "contextid": 6,
            "coursenamebankname": "c3 - Question bank 5",
            "cminfo": {},
            "questioncategories": []
        }
    ],
    "hassharedbanks": true,
    "sharedbanks": [
        {
            "name": "Question bank 1",
            "modid": "2",
            "contextid": 2,
            "coursenamebankname": "c1 - Question bank 1",
            "cminfo": {},
            "questioncategories": []
        },
        {
            "name": "Question bank 2",
            "modid": "3",
            "contextid": 3,
            "coursenamebankname": "c1 - Question bank 2",
            "cminfo": {},
            "questioncategories": []
        },
        {
            "name": "Question bank 3",
            "modid": "4",
            "contextid": 4,
            "coursenamebankname": "c2 - Question bank 4",
            "cminfo": {},
            "questioncategories": []
        },
        {
            "name": "Question bank 4",
            "modid": "6",
            "contextid": 6,
            "coursenamebankname": "c3 - Question bank 5",
            "cminfo": {},
            "questioncategories": []
        }
    ]
}
}}
<div class="quiz-bank">
    <h5>{{#str}}quizquestionbank, core_question{{/str}}</h5>
    <a href="#" class="ms-3" data-newmodid="{{quizcmid}}">{{quizname}}</a>
</div>

<hr class="w-75">

{{#hascoursesharedbanks}}
    <div class="course-shared-banks">
        <h5>{{#str}}banksincourse, core_question{{/str}}</h5>
        {{#coursesharedbanks}}
            <ul class="list-unstyled ms-3">
                <li>
                    <a href="#" data-newmodid="{{modid}}">{{name}}</a>
                </li>
            </ul>
        {{/coursesharedbanks}}
    </div>
    <hr class="w-75">
{{/hascoursesharedbanks}}

{{#hasrecentlyviewedbanks}}
    <div class="recently-viewed-banks">
        <h5>{{#str}}recentlyviewedquestionbanks, core_question{{/str}}</h5>
        {{#recentlyviewedbanks}}
            <ul class="list-unstyled ms-3">
                <li>
                    <a href="#" data-newmodid="{{modid}}">{{coursenamebankname}}</a>
                </li>
            </ul>
        {{/recentlyviewedbanks}}
    </div>
    <hr class="w-75">
{{/hasrecentlyviewedbanks}}


<div class="search-banks">
    <h5>{{#str}}otherquestionbank, core_question{{/str}}</h5>
    <select class="form-select" id="searchbanks" data-contextid="{{quizcontextid}}">
    </select>
</div>
