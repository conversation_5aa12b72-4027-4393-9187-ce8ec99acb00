{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/qbank_action_menu

    Question bank tertiary navigtion.

    Classes required for JS:
    * none

    Context variables required for this template:
    * see question/classes/output/qbank_actionbar.php

    Example context (json):
    {
        "questionbankselect": {
            "action": "http://localhost/moodle/course/jumpto.php",
            "classes": "urlselect",
            "formid": "questionbankaction",
            "id": "url_select61a85bc543dca7",
            "label": "Question bank tertiary navigation",
            "options": [{
                    "name": "Questions",
                    "selected": true,
                    "value": "/question/edit.php?cmid=7"
                },
                {
                    "name": "Categories",
                    "selected": false,
                    "value": "/question/bank/managecategories/category.php?cmid=7"
                },
                {
                    "name": "Import",
                    "selected": false,
                    "value": "/question/bank/importquestions/import.php?cmid=7"
                },
                {
                    "name": "Export",
                    "selected": false,
                    "value": "/question/bank/exportquestions/export.php?cmid=7"
                }
            ]
        },
        "actionbutton": {
            "url": "http://localhost/moodle/question/bank/managecategories/addcategory.php?cmid=71",
            "label": "Add category"
        }
    }
}}
<div class="tertiary-navigation">
    <div class="d-flex">
        {{#questionbankselect}}
            <div class="navitem">
                {{>core/url_select}}
            </div>
        {{/questionbankselect}}
        {{#actionbutton}}
            <div class="navitem">
                <a class="btn btn-primary ms-2" href="{{url}}">{{label}}</a>
            </div>
        {{/actionbutton}}
    </div>
</div>
