{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/showtext_option

    Example context (json):
    {
        "selected1": true
    }
}}
<div>
    <label for="qbshowtext">{{#str}} showquestiontext, question {{/str}}</label>
    <select id="qbshowtext" name="qbshowtext" class="searchoptions form-select align-baseline">
        <option value="0"{{#selected0}} selected{{/selected0}}>{{#str}} showquestiontext_off, question {{/str}}</option>
        <option value="1"{{#selected1}} selected{{/selected1}}>{{#str}} showquestiontext_plain, question {{/str}}</option>
        <option value="2"{{#selected2}} selected{{/selected2}}>{{#str}} showquestiontext_full, question {{/str}}</option>
    </select>
</div>
