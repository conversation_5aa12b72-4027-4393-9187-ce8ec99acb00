{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/qbank_filter

    Template for the form containing one or more filter rows.

    Example context (json):
    {
        "tableregionid": "qbank-table",
        "courseid": "1",
        "categoryid": "2",
        "perpage": "100",
        "contextid": "3",
        "quizcmid": "4",
        "component": "core_quiz",
        "callback": "get_question_data",
        "view": "\\core\\question\\local\\bank\\view",
        "cmid": "4",
        "pagevars": "[]",
        "extraparams": "[]"
    }
}}

<form method="dialog">
{{> core/datafilter/filter}}
</form>

{{#js}}
    require(['core_question/filter'], function(Filter) {
        Filter.init(
            {{#quote}}core-filter-{{uniqid}}{{/quote}},
            {{courseid}},
            {{categoryid}},
            {{perpage}},
            {{contextid}},
            {{quizcmid}},
            {{#quote}}{{component}}{{/quote}},
            {{#quote}}{{callback}}{{/quote}},
            {{#quote}}{{view}}{{/quote}},
            {{cmid}},
            {{{pagevars}}},
            {{{extraparams}}},
        );
    });
{{/js}}
