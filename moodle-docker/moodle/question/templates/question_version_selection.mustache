{{!
    This file is part of Moodle - http://moodle.org/
    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.
    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.
    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/question_version_selection

    The version selection for question bank.
    * options - Versions of the question
    * uniqueidentifier - identifier to target the change from js

    Example context (json):
    {
        "options": [{
            "name": "Version 1",
            "selected": true,
            "value": "10"
            },
            {
            "name": "Version 2",
            "selected": false,
            "value": "20"
        }],
        "uniqueidentifier": "question_comment_version_selection"
    }
}}

<select class="question_version_dropdown form-select mb-3" aria-label="{{#str}}question_version, core_question{{/str}}" name="question_version_dropdown" id="{{uniqueidentifier}}">
    {{#options}}
        <option value="{{questionid}}" {{#selected}}selected{{/selected}}>{{name}}</option>
    {{/options}}
</select>
