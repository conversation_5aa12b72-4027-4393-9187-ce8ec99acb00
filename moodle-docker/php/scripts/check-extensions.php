#!/usr/bin/env php
<?php
/**
 * Moodle 5.0 PHP Extensions Checker
 * Verifies all required and recommended PHP extensions are loaded
 */

echo "=== Moodle 5.0 PHP Extensions Checker ===\n\n";

// Required extensions for Moodle 5.0
$required_extensions = [
    'ctype',
    'curl',
    'dom',
    'gd',
    'iconv',
    'intl',
    'json',
    'mbstring',
    'pcre',
    'simplexml',
    'spl',
    'xml',
    'zip',
    'mysqli', // Database extension
    'pdo',
    'pdo_mysql'
];

// Recommended extensions for Moodle 5.0
$recommended_extensions = [
    'openssl',
    'soap',
    'sodium',
    'tokenizer',
    'sockets',
    'redis',
    'opcache',
    'exif'
];

// Optional extensions (nice to have but not critical)
$optional_extensions = [
    'ldap'  // Only needed for LDAP authentication
];

$missing_required = [];
$missing_recommended = [];
$missing_optional = [];

echo "Checking Required Extensions:\n";
echo "============================\n";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ $ext - LOADED\n";
    } else {
        echo "✗ $ext - MISSING\n";
        $missing_required[] = $ext;
    }
}

echo "\nChecking Recommended Extensions:\n";
echo "===============================\n";
foreach ($recommended_extensions as $ext) {
    // Special check for opcache which might be loaded as Zend extension
    if ($ext === 'opcache') {
        $opcache_enabled = ini_get('opcache.enable') == '1' || ini_get('opcache.enable') === true;
        if (extension_loaded('opcache') || extension_loaded('Zend OPcache') ||
            function_exists('opcache_get_status') || $opcache_enabled) {
            echo "✓ $ext - LOADED\n";
        } else {
            echo "✗ $ext - MISSING\n";
            $missing_recommended[] = $ext;
        }
    } else {
        if (extension_loaded($ext)) {
            echo "✓ $ext - LOADED\n";
        } else {
            echo "✗ $ext - MISSING\n";
            $missing_recommended[] = $ext;
        }
    }
}

echo "\nChecking Optional Extensions:\n";
echo "============================\n";
foreach ($optional_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ $ext - LOADED\n";
    } else {
        echo "○ $ext - NOT LOADED (optional)\n";
        $missing_optional[] = $ext;
    }
}

echo "\n=== PHP Configuration Check ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "Max Input Vars: " . ini_get('max_input_vars') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "File Uploads: " . (ini_get('file_uploads') ? 'On' : 'Off') . "\n";
echo "Session Auto Start: " . (ini_get('session.auto_start') ? 'On' : 'Off') . "\n";

echo "\n=== Summary ===\n";
if (empty($missing_required)) {
    echo "✓ All required extensions are loaded!\n";
} else {
    echo "✗ Missing required extensions: " . implode(', ', $missing_required) . "\n";
}

if (empty($missing_recommended)) {
    echo "✓ All recommended extensions are loaded!\n";
} else {
    echo "⚠ Missing recommended extensions: " . implode(', ', $missing_recommended) . "\n";
}

if (!empty($missing_optional)) {
    echo "ℹ Optional extensions not loaded: " . implode(', ', $missing_optional) . " (can be added later if needed)\n";
}

// Check specific Moodle 5.0 requirements
echo "\n=== Moodle 5.0 Specific Checks ===\n";

// Check PHP version requirement (8.2-8.4)
$php_version = PHP_VERSION;
if (version_compare($php_version, '8.2.0', '>=') && version_compare($php_version, '8.5.0', '<')) {
    echo "✓ PHP Version $php_version is supported for Moodle 5.0\n";
} else {
    echo "✗ PHP Version $php_version is NOT supported for Moodle 5.0 (requires 8.2-8.4)\n";
}

// Check max_input_vars requirement (>= 5000)
$max_input_vars = ini_get('max_input_vars');
if ($max_input_vars >= 5000) {
    echo "✓ max_input_vars ($max_input_vars) meets Moodle 5.0 requirement (>= 5000)\n";
} else {
    echo "✗ max_input_vars ($max_input_vars) does NOT meet Moodle 5.0 requirement (>= 5000)\n";
}

// Check sodium extension (required for Moodle 5.0)
if (extension_loaded('sodium')) {
    echo "✓ Sodium extension is loaded (required for Moodle 5.0)\n";
} else {
    echo "✗ Sodium extension is MISSING (required for Moodle 5.0)\n";
}

echo "\n=== Connection Tests ===\n";

// Test Redis connection if extension is loaded
if (extension_loaded('redis')) {
    try {
        $redis = new Redis();
        $redis_host = getenv('MOODLE_REDIS_HOST') ?: 'redis';
        $redis_port = getenv('MOODLE_REDIS_PORT') ?: 6379;

        if ($redis->connect($redis_host, $redis_port)) {
            echo "✓ Redis connection successful ($redis_host:$redis_port)\n";
            $redis->close();
        } else {
            echo "✗ Redis connection failed ($redis_host:$redis_port)\n";
        }
    } catch (Exception $e) {
        echo "✗ Redis connection error: " . $e->getMessage() . "\n";
    }
}

// Test MySQL connection
try {
    $db_host = getenv('MOODLE_DB_HOST') ?: 'mysql';
    $db_name = getenv('MOODLE_DB_NAME') ?: 'moodle';
    $db_user = getenv('MOODLE_DB_USER') ?: 'moodle';
    $db_pass = getenv('MOODLE_DB_PASS') ?: '';
    $db_port = getenv('MOODLE_DB_PORT') ?: 3306;

    // Try connection with MySQL 8.4+ compatible options
    $dsn = "mysql:host=$db_host;port=$db_port;dbname=$db_name;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ];

    $pdo = new PDO($dsn, $db_user, $db_pass, $options);
    echo "✓ MySQL connection successful ($db_host:$db_port)\n";
} catch (Exception $e) {
    // Try without specifying database (in case it doesn't exist yet)
    try {
        $dsn = "mysql:host=$db_host;port=$db_port;charset=utf8mb4";
        $pdo = new PDO($dsn, $db_user, $db_pass, $options);
        echo "✓ MySQL connection successful ($db_host:$db_port) - database server reachable\n";
    } catch (Exception $e2) {
        echo "✗ MySQL connection error: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Check Complete ===\n";

// Exit with error code if required extensions are missing
if (!empty($missing_required)) {
    exit(1);
}

exit(0);
