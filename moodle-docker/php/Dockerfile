FROM php:8.3-fpm

# Install system dependencies for PHP extensions
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libxml2-dev \
    libicu-dev \
    libcurl4-openssl-dev \
    libonig-dev \
    libsodium-dev \
    libssl-dev \
    libpq-dev \
    zip \
    unzip \
    git \
    cron \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure and install required PHP extensions for Moodle 5.0
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        gd \
        mysqli \
        zip \
        intl \
        soap \
        exif \
        opcache \
        curl \
        dom \
        simplexml \
        mbstring \
        sodium \
        pdo \
        pdo_mysql \
        sockets

# Install Redis extension via PECL
RUN pecl install redis \
    && docker-php-ext-enable redis

# Note: Built-in extensions in PHP 8.3: ctype, iconv, json, pcre, spl, openssl, xml, tokenizer
# Note: xmlrpc was removed in PHP 8.0+ and is no longer available

# Set recommended PHP.ini settings
COPY custom-configs/php.ini /usr/local/etc/php/conf.d/php.ini
COPY custom-configs/php-fpm.conf /usr/local/etc/php-fpm.d/php-fpm.conf

# Setup Moodle cron job
COPY cron/moodle-cron /etc/cron.d/moodle-cron
RUN chmod 0644 /etc/cron.d/moodle-cron \
    && crontab /etc/cron.d/moodle-cron

# Create startup script and extension checker
COPY scripts/start.sh /start.sh
COPY scripts/check-extensions.php /usr/local/bin/check-extensions.php
RUN chmod +x /start.sh \
    && chmod +x /usr/local/bin/check-extensions.php

WORKDIR /var/www/html

CMD ["/start.sh"]